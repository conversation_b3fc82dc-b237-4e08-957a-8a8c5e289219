<template>
  <nav>
    <div v-if="isLargeScreen">
      <ul>
        <li v-for="item in items" :key="item.label">
          <router-link
            :to="{ name: item.name, params: item.params }"
            :class="$route.name === item.name && 'router-link-active'"
          >
            <i :class="'icon icon-' + item.icon"></i>
            {{ item.label }}
          </router-link>
        </li>
      </ul>
    </div>
    <div v-else>
      <select class="custom-select" v-model="selectedRoute">
        <option
          v-for="item in items"
          :key="item.label"
          :value="item.name"
        >
          {{ item.label }}
        </option>
      </select>
    </div>
  </nav>
</template>

<script>
import { stringsMixin } from '@/mixins/strings'
import { mapState } from 'vuex'

export default {
  name: 'CustomNavbar',

  mixins: [stringsMixin],

  props: {

  },

  data() {
    return {
      items: [],
      selectedRoute: '',
      isLargeScreen: true
    }
  },

  computed: {
    ...mapState({
      screenWidth: (state) => state.screenWidth
    }),
    trailId() {
      return this.$route.params.trailId
    }
  },

  watch: {
    screenWidth(val) {
      this.checkScreenSize(val)
    },
    selectedRoute(newValue) {
      if (newValue) {
        this.$router.push({ name: newValue })
      }
    }
  },

  created() {
    this.setRouteLinks()
    this.checkScreenSize(window.innerWidth)
    window.addEventListener('resize', this.handleResize)
    this.selectedRoute = this.$route.name
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    setRouteLinks() {
      this.items = this.trailId ? this.getUpdateRoutes() : this.getCreateRoutes()
    },

    checkScreenSize(width) {
      this.isLargeScreen = width >= 992 // LG breakpoint
    },

    handleResize() {
      this.checkScreenSize(window.innerWidth)
    },

    getCreateRoutes() {
      return [
        {
          //label: this.strings.trail_config,
          label: 'Meus Certificados',
          name: 'my.certificates.index',
          icon: 'certificate'
        },
        {
          //label: this.strings.trail_config,
          label: 'Certificados de Trilhas',
          name: 'trails.certificates.index',
          icon: 'trails'
        },
      ]
    },

    getUpdateRoutes() {
      return [
        {
          //label: this.strings.trail_config,
          label: 'Meus Certificados',
          name: 'my.certificates.index',
          icon: 'certificate'
        },
        {
          //label: this.strings.trail_config,
          label: 'Certificados de Trilhas',
          name: 'trails.certificates.index',
          icon: 'gear'
        },
      ]
    }
  }
}
</script>

<style scoped lang="scss">
#root-certificatepage {
  nav {
    background-color: transparent;
    padding: 10px 0;
    border-bottom: 1px solid #adb5bd;
    margin-bottom: 30px;
  }

  ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    align-items: center;
  }

  a,
  span {
    text-decoration: none;
    color: #ffffffa6;
    font-size: 16px;
    font-weight: 500;
    position: relative;
    padding: 5px 16px;
    transition: all 0.3s;
    display: flex;
    gap: 10px;
    align-items: center;
    box-shadow: unset;

    &.router-link-active {
      color: #fff;
      &::after {
        background-color: #6ea8fe;
        content: '';
        position: absolute;
        bottom: -11px;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 4px;
        transition: background-color 0.3s;
      }
    }

    .icon {
      font-size: 20px;
      margin: 0;
      height: 20px;
      width: auto;
    }
  }
  .custom-select {
    background-color: #212529;
    color: #fff;
    padding: 8px;
    border-radius: 4px;
    width: 100%;
  }
}
</style>
