{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_smart/default

    Admin time setting template.

    Smart default layout template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * navdraweropen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON>od<PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "navdraweropen":true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false
    }
}}

{{> theme_smart/head }}

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}
{{{ output.standard_top_of_body_html }}}

<div id="page-wrapper" class="d-print-block">

    <div id="page" class="container-fluid d-print-block">

        <div id="page-content" class="row d-print-block">
			
            <div class="site-wrapper w-100 d-flex justify-content-stretch">
				{{> theme_smart/menubar}}

				<div id="region-main-box" class="d-flex flex-column w-100 px-3 px-lg-5">
					<section id="region-main" aria-label="{{#str}}content{{/str}}">
						
						{{{ output.full_header }}}

						{{> theme_smart/navbar }}

						{{#hasregionmainsettingsmenu}}
						<div id="region-main-settings-menu" class="d-print-none">
							<div> {{{ regionmainsettingsmenu }}} </div>
						</div>
						{{/hasregionmainsettingsmenu}}
						
						{{#secondarymoremenu}}
							<div class="secondary-navigation d-print-none">
								{{> core/moremenu}}
							</div>
						{{/secondarymoremenu}}

						{{#hasblocks}}
							{{{ sidepreblocks }}}
							<!--{{{ addblockbutton }}}-->
						{{/hasblocks}}


						{{#hasregionmainsettingsmenu}}
							<div class="region_main_settings_menu_proxy"></div>
						{{/hasregionmainsettingsmenu}}
						
						{{{ output.course_content_header }}}
						
						{{#headercontent}}
							{{> core/activity_header }}
						{{/headercontent}}
						
						{{#overflow}}
							<div class="container-fluid tertiary-navigation">
								<div class="navitem">
									{{> core/url_select}}
								</div>
							</div>
						{{/overflow}}
						
						{{{ output.main_content }}}
						{{{ output.activity_navigation }}}
						{{{ output.course_content_footer }}}

					</section>
					
					{{{ output.standard_after_main_region_html }}}
					
					{{> theme_smart/footer }}
				</div>
			</div>
        </div>
    </div>
</div>

{{{ output.smart_custom_html }}}

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader', 'theme_smart/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_smart/loader');
});
{{/js}}
