{{!
	{
	  "courseinfo": {
			"aboutcourse_data": [
                {
                    "index": 0,
                    "shortname": "",
                    "name": "",
                    "data": "",
                    "term": {
                        "accepted": false,
                        "tag": ""
                    }
                }
            ]
		}
	}
}}

<li id="section-about" class="section course-section main clearfix border-bottom py-3"
    data-sectionid="about" data-sectionreturnid="0" data-for="section" data-id="{{ initialsectionid }}" data-number="0">

    <div class="course-section-header d-flex" data-for="section_title" data-id="{{ initialsectionid }}" data-number="0">

        <div class="d-flex align-items-start position-relative custom-link">

            <a role="button" data-toggle="collapse" data-for="sectiontoggler" href="#coursecontentcollapseabout" 
                id="collapssesectionabout" class="icons-collapse-expand justify-content-center collapsed"
                aria-expanded="false" aria-controls="coursecontentcollapseabout" aria-label="Sobre o curso">

                <span class="expanded-icon icon-no-margin p-2" title="{{#str}} collapse, core {{/str}}">
                    {{#pix}} t/expandedchevron, core {{/pix}}
                </span>
                <span class="collapsed-icon icon-no-margin p-2" title="{{#str}} expand, core {{/str}}">
                    <span class="dir-rtl-hide">{{#pix}} t/collapsedchevron, core {{/pix}}</span>
                    <span class="dir-ltr-hide">{{#pix}} t/collapsedchevron_rtl, core {{/pix}}</span>
                </span>

                <h3 class="sectionname course-content-item d-flex align-self-stretch align-items-center ml-3 mb-0"
                    id="sectionid-about-title" data-for="section_title" data-id="{{ initialsectionid }}" data-number="0">
                    {{#str}} course:about, format_learningflix {{/str}}
                </h3>
            </a>
        </div>
    </div>

    <div id="coursecontentcollapseabout" class="content {{^sitehome}} course-content-item-content collapse {{/sitehome}} mt-3">
    
        <ul class="">
            {{#courseinfo.aboutcourse_data }}
                {{> format_learningflix/local/course_about_item }}
            {{/courseinfo.aboutcourse_data }}
        </ul>
    </div>
</li>
