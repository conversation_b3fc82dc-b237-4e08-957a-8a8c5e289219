{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_smart/login

    Login page template

    Example context (json):
    {
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Login page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headers keep HTML validators happy</h1>"
        }
    }
}}
{{> theme_smart/head }}

<style>
    #fitem_id_passwordpolicyinfo .form-control-static{
        display:none;
    }
</style>

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}

<div id="page-wrapper">

    {{{ output.standard_top_of_body_html }}}

    <div id="page" class="container-fluid pt-5 px-md-0 mt-0">
        <div id="page-content" class="row mx-0">
            <div id="region-main-box" class="col-12">
                <section id="region-main" class="col-12 h-100" aria-label="{{#str}}content{{/str}}">
                <div class="login-wrapper">
                    <div class="login-container bg-glassy">
                        <input id="show-password-lang" type="hidden" value="{{#str}}login:showpassword, theme_smart{{/str}}">
                        <input id="hide-password-lang" type="hidden" value="{{#str}}login:hiddenpassword, theme_smart{{/str}}">
                    {{{ output.main_content }}}
                    </div>
                </div>
                </section>
            </div>
        </div>
		
		{{> theme_smart/loginfooter }}
    </div>
</div>

{{{ output.smart_custom_html }}}

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader'], function() {
	M.util.js_complete('theme_smart/loader');

    let password_arialabel = "{{#str}}signup:password_arialabel, theme_smart{{/str}}";
    $("#page-login-signup input#password").attr("aria-label", password_arialabel)
    $("#fitem_id_passwordpolicyinfo .form-control-static").html(password_arialabel).show()
  
	if($("#id_searchbyusernamecontainer").length){
		$forgotPasswordForm = $("#page-login-forgot_password form");
		$generalbox = $("#page-login-forgot_password .generalbox");

		$('<p class="text-center lead mb-2">{{#str}}login:lostpassword, theme_smart{{/str}}</p>').insertBefore($generalbox);
		$('<div class="text-center mt-4"><a href="{{config.wwwroot}}/login/index.php">{{#str}}login:backtologin, theme_smart{{/str}}</a></div>').insertAfter($forgotPasswordForm);
	}
	
	$("#fitem_id_username2 .felement .form-control-static").each(function(){
		$(this).removeClass("form-control-static");
		$(this).addClass("form-control text-muted mt-1")
		$(this).attr("readonly", true);
	});

    const h4Element = document.querySelector('#modal-header h4');
    if (h4Element) {
        const h2Element = document.createElement('h2');
        h2Element.textContent = h4Element.textContent;
        h2Element.style.fontSize = '1.40625rem';
        h4Element.parentNode.replaceChild(h2Element, h4Element);
    }
	
	$("#fgroup_id_buttonar .btn-cancel .btn-secondary").click(function(e){
		e.preventDefault();
		window.location.href = "{{config.wwwroot}}/login/index.php";
	})
	.removeClass("btn-secondary").addClass("btn-dark");

    
    var feedbackElements = document.querySelectorAll(".form-control-feedback.invalid-feedback");
    // adiciona tabindex -1 nos elementos
    setTimeout(function() {
        feedbackElements.forEach(function(element) {
            element.setAttribute("tabindex", "-1");
        });
    }, 2000);

    // Define um listener para verificar se o elemento tem texto e se tiver então volta o focus para ele.
    // Função para criar e iniciar o observer para um elemento
    const observeElement = (element) => {
    // Cria um MutationObserver para monitorar mudanças no textContent
    const observer = new MutationObserver((mutationsList) => {
        for (let mutation of mutationsList) {
        if ((mutation.type === 'childList' || mutation.type === 'characterData') && mutation.target.textContent.trim().length > 0) {
            // Devolve o foco para o elemento
            mutation.target.focus();
            // Desconecta o observador para este elemento
            observer.disconnect();
            break; // Parar de processar após focar no elemento
        }
        }
    });

    // Configura o observer para observar mudanças no conteúdo dos nós de texto
    const observerConfig = { childList: true, characterData: true, subtree: true };

    // Inicia a observação para o elemento
    observer.observe(element, observerConfig);
    };

    // Inicia a observação para cada elemento selecionado
    feedbackElements.forEach(element => {
    observeElement(element);
    });

    function initializeAriaLive() {
        // Cria ou seleciona o contêiner aria-live
        var ariaLiveContainer = document.getElementById('aria-live-container');
        if (!ariaLiveContainer) {
            ariaLiveContainer = document.createElement('div');
            ariaLiveContainer.id = 'aria-live-container';
            ariaLiveContainer.setAttribute('aria-live', 'polite');
            ariaLiveContainer.className = 'sr-only'; // Adiciona a classe sr-only existente
            document.body.appendChild(ariaLiveContainer);
        }
    
        var submitButton = document.getElementById('id_submitbutton');
        var cancelButton = document.getElementById('id_cancel');
    
        function addAriaLive(message) {
            // Atualiza o conteúdo do contêiner aria-live
            ariaLiveContainer.textContent = message;
    
            // Remove o conteúdo do contêiner aria-live após 1 segundo
            setTimeout(function() {
                ariaLiveContainer.textContent = '';
            }, 1000);
        }
    
        if (submitButton) {
            submitButton.setAttribute('aria-label', submitButton.value);
            submitButton.setAttribute('tabindex', '0');
    
            submitButton.addEventListener('focus', function() {
                addAriaLive(submitButton.value);
            });
        }
    
        if (cancelButton) {
            cancelButton.setAttribute('aria-label', cancelButton.value);
            cancelButton.setAttribute('tabindex', '0');
    
            cancelButton.addEventListener('focus', function() {
                addAriaLive(cancelButton.value);
            });
        }
    }
    initializeAriaLive();

    // accesibilidade botão mostrar/esconder senha
    $(function() {
        // Pega os valores de texto para mostrar/esconder a senha
        var showPasswordText = $('#show-password-lang').val();
        var hidePasswordText = $('#hide-password-lang').val();
    
        // Substitui os elementos <span> por <a> e simula um clique para corrigir o ícone
        $("#password-toggle, #passwordconfirm-toggle").each(function() {
            var $this = $(this);
            var style = $this.attr('style'); // Armazena o estilo atual
            var newElement = $('<a/>').attr({
                'id': $this.attr('id'),
                'onclick': $this.attr('onclick'),
                'role': 'button',
                'style': style + ' cursor: pointer; color: #ced4da !important;',
                'tabindex': '0',
                'data-show-password': showPasswordText,
                'data-hide-password': hidePasswordText,
                'aria-label': showPasswordText  // Inicia com "Mostrar senha" como padrão
            }).html($this.html());
    
            $this.replaceWith(newElement);
        });
    
        // Move cada toggle para abaixo do campo de senha correspondente
        $('#password-toggle').insertAfter('#password');
        $('#passwordconfirm-toggle').insertAfter('#passwordconfirm');

        // Atualiza os estilos inline dos elementos movidos
        $('#password-toggle, #passwordconfirm-toggle').css({
            'margin-top': '',
            'top': '11px',
            'right': '8px'
        });
    
        // Evento de clique para alternar a visibilidade da senha
        $(document).on("click keypress", "#password-toggle, #passwordconfirm-toggle", function(event) {
            if (event.type === "click" || (event.type === "keypress" && event.keyCode === 13)) {
                var toggleId = this.id;
                var inputId = (toggleId === "password-toggle") ? "#password" : "#passwordconfirm";
                var iconId = (toggleId === "password-toggle") ? "#password-icon" : "#passwordconfirm-icon";
                var input = $(inputId);
                var icon = $(iconId);
        
                if (icon.hasClass("fa-eye-slash")) {
                    // Senha está sendo mostrada
                    $(this).attr("aria-label", $(this).data("hide-password"));
                } else {
                    // Senha está sendo ocultada
                    $(this).attr("aria-label", $(this).data("show-password"));
                }
            }
        });
    });        
});
{{/js}}
