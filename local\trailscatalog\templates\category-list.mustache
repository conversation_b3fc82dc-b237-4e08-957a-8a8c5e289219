<div class="" id="category-list">
    {{#categories}}
        <div class="d-flex align-items-center justify-content-between border-bottom mb-2 py-2">
            <div class="{{^visible}}dimmed{{/visible}} list-item custom-link">
                <a href="{{url}}" class="list-item-link">
                    <h5 class="m-0">
                        <span class="text-body">{{name}}</span> ({{coursecount}})
                    </h5>
                </a>
            </div>
            {{#is_admin}}
            <div class="dropdown">
                <a href="#" class="btn btn-dark rounded-circle" data-toggle="dropdown" aria-label="{{#str}}category_options,local_trailscatalog,{{name}}{{/str}}" aria-expanded="false">
                    <i class="fa fa-ellipsis-h"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <a class="dropdown-item" href="{{url}}">{{#str}}view,local_trailscatalog{{/str}}</a>

                    {{#canedit}}
                        <a class="dropdown-item btn-edit-category" href="#" data-parent="{{parent}}" data-id="{{id}}" data-title="{{#str}}edit_category_options,local_trailscatalog{{/str}}">{{#str}}edit,local_trailscatalog{{/str}}</a>

                        {{#visible}}
                        <a class="dropdown-item" href="index.php?action=hidecategory&categoryid={{id}}&parentcategory={{parent}}">{{#str}}hide,local_trailscatalog{{/str}}</a>
                        {{/visible}}

                        {{^visible}}
                        <a class="dropdown-item" href="index.php?action=showcategory&categoryid={{id}}&parentcategory={{parent}}">{{#str}}show,local_trailscatalog{{/str}}</a>
                        {{/visible}}
                    {{/canedit}}
                </div>
            </div>
            {{/is_admin}}
        </div>
    {{/categories}}
</div>

{{#is_admin}}
    {{>tool_coursemanagement/category_form}}
{{/is_admin}}