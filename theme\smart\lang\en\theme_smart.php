<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language file.
 *
 * @package   theme_smart
 * @copyright 2023 Revvo
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['advancedsettings'] = 'Advanced settings';
$string['backgroundimage'] = 'Background image';
$string['backgroundimage_desc'] = 'The image to display as a background of the site. The background image you upload here will override the background image in your theme preset files.';
$string['brandcolor'] = 'Brand colour';
$string['brandcolor_desc'] = 'The accent colour.';
$string['bootswatch'] = 'Bootswatch';
$string['bootswatch_desc'] = 'A bootswatch is a set of Bootstrap variables and css to style Bootstrap';
$string['choosereadme'] = 'Theme is a modern highly-customisable theme. This theme is intended to be used directly, or as a parent theme when creating new themes utilising Bootstrap 4.';
$string['configtitle'] = 'Theme';
$string['event'] = 'Event';
$string['generalsettings'] = 'General settings';
$string['loginbackgroundimage'] = 'Login page background image';
$string['loginbackgroundimage_desc'] = 'The image to display as a background for the login page.';
$string['footerlogo'] = "Footer logo";
$string['footerlogo_desc'] = "add logo to show in footer";
$string['nobootswatch'] = 'None';
$string['pluginname'] = 'Theme';
$string['presetfiles'] = 'Additional theme preset files';
$string['presetfiles_desc'] = 'Preset files can be used to dramatically alter the appearance of the theme. See <a href="https://docs.moodle.org/dev/Theme_Presets">Theme presets</a> for information on creating and sharing your own preset files, and see the <a href="https://archive.moodle.net/theme">Presets repository</a> for presets that others have shared.';
$string['preset'] = 'Theme preset';
$string['preset_desc'] = 'Pick a preset to broadly change the look of the theme.';
$string['privacy:metadata'] = 'The Theme theme does not store any personal data about any user.';
$string['rawscss'] = 'Raw SCSS';
$string['rawscss_desc'] = 'Use this field to provide SCSS or CSS code which will be injected at the end of the style sheet.';
$string['rawscsspre'] = 'Raw initial SCSS';
$string['rawscsspre_desc'] = 'In this field you can provide initialising SCSS code, it will be injected before everything else. Most of the time you will use this setting to define variables.';
$string['region-side-pre'] = 'Right';

$string['coursesettings'] = 'Course Settings';
$string['activitynavigationmods'] = 'Enable navigation in modules';
$string['activitynavigationmods_desc'] = 'Select the modules that will have activity navigation enabled select none to disable';

$string['social_title'] = 'Social Media';
$string['socialsettings'] = 'Social Media Settings';
$string['companyaddress'] = 'Company Address';
$string['social_facebook'] = 'Facebook';
$string['social_twitter'] = 'Twitter';
$string['social_instagram'] = 'Instagram';
$string['social_linkedin'] = 'LinkedIn';
$string['social_youtube'] = 'YouTube';
$string['social_whatsapp'] = 'WhatsApp';

$string['apps_title'] = 'App Stores';
$string['app_applestore'] = 'Apple Store';
$string['app_googleplay'] = 'Google Play';

$string['aisettings'] = 'AI Settings';
$string['ai_title'] = 'Judy integration settings';
$string['enable_ai_menu'] = 'Enable Judy in menu';
$string['enable_ai_menu_desc'] = 'Enable or disable Judy integration in menu.';
$string['ai_assistantid'] = 'Assistant ID';

$string['privacy'] = 'Privacy';
$string['terms'] = 'Terms';
$string['credits'] = '© ' . date("Y") . ' Developed by ';

$string['ranking'] = 'Ranking';
$string['newpassword'] = "New password";
$string['newpassword_help'] = "The password must be at least 8 characters long, contain at least 1 digit, at least 1 lowercase letter, at least 1 uppercase letter, and at least 1 non-alphanumeric character, such as *, dash, or hashtag.";

$string['login:access'] = "Access";
$string['login:rememberme'] = "Remember me";
$string['login:forgotpassword'] = "Forgot passowrd";
$string['login:loginwith'] = "Login with";
$string['login:backtologin'] = "Back to login";
$string['login:lostpassword'] = "Lost your passowrd?";
$string['loginlogo'] = "Login logo";
$string['loginlogo_desc'] = "add a logo which will show in login form";
$string['login:showpassword'] = "Show password";
$string['login:hiddenpassword'] = "Hide password";
$string['signup:username_arialabel'] = "User identification, This field is used to create a unique username on the platform. Please enter a combination of letters, numbers and special characters. Avoid using spaces or accented characters. Examples of valid names: 'user123'";
$string['signup:password_arialabel'] = "Password. ".$string['newpassword_help'];

// MENU BAR //
$string['adminmenu_name'] = 'Management';
$string['admin_submenu_manageusers'] = 'Manage Users';
$string['admin_submenu_managecourses'] = 'Manage Courses';

// COURSE //
$string['exitcourse'] = 'Exit course';
$string['coursemenu'] = 'Course Menu';

$string['showfooter'] = 'Show footer';
$string['unaddableblocks'] = 'Unneeded blocks';
$string['unaddableblocks_desc'] = 'The blocks specified are not needed when using this theme and will not be listed in the \'Add a block\' menu.';
$string['privacy:metadata:preference:draweropenblock'] = 'The user\'s preference for hiding or showing the drawer with blocks.';
$string['privacy:metadata:preference:draweropenindex'] = 'The user\'s preference for hiding or showing the drawer with course index.';
$string['privacy:metadata:preference:draweropennav'] = 'The user\'s preference for hiding or showing the drawer menu navigation.';
$string['privacy:drawerindexclosed'] = 'The current preference for the index drawer is closed.';
$string['privacy:drawerindexopen'] = 'The current preference for the index drawer is open.';
$string['privacy:drawerblockclosed'] = 'The current preference for the block drawer is closed.';
$string['privacy:drawerblockopen'] = 'The current preference for the block drawer is open.';

$string['footer:sitemap'] = "Sitemap";
$string['footer:socialmedia'] = "Social media";
$string['footer:apps'] = "Apps";

$string['breadcrumbs:home'] = "Home";
$string['breadcrumbs:catalog'] = "Catalog";
$string['catalog'] = "Catalog";

$string['placeholder:search'] = "Search...";

$string['message:plural'] = "Messages";
$string['message:seeall'] = "See All";
$string['message:noncontacts'] = "Contacts";

$string['forum:format'] = "Format";
$string['forum:discussion'] = "discussion";
$string['forum:parent'] = "parent";
$string['forum:userfullname'] = "userfullname";
$string['forum:created'] = "created";
$string['forum:modified'] = "modified";
$string['forum:mailed'] = "mailed";
$string['forum:subject'] = "subject";
$string['forum:message'] = "message";
$string['forum:messageformat'] = "messageformat";
$string['forum:messagetrust'] = "messagetrust";
$string['forum:attachment'] = "attachment";
$string['forum:totalscore'] = "totalscore";
$string['forum:mailnow'] = "mailnow";
$string['forum:deleted'] = "deleted";
$string['forum:privatereplyto'] = "privatereplyto";
$string['forum:privatereplytofullname'] = "privatereplytofullname";
$string['forum:wordcount'] = "wordcount";
$string['forum:charcount'] = "charcount";
$string['enable_mustaches_footer'] = 'Enable footer for administrador';
$string['enable_mustaches_footer_desc'] = 'Enable or disable the display for administrador.';
$string['footer_hiden_user'] = 'Enable footer for user';
$string['footer_hiden_user_desc'] = 'Enable or disable footer for user.';
$string['enable_login_footer'] = 'Enable footer for login page';
$string['enable_login_footer_desc'] = 'Enable or disable the display for login page.';

$string['btn_primary_bg_color'] = "Primary color";
$string['btn_primary_bg_color_desc'] = "Primary color button";
$string['btn_primary_text_color'] = "Primary text color";
$string['btn_primary_text_color_desc'] = "Primary text color button";

$string['required'] = 'Required';

$string['catalog'] = 'Catalog';
$string['faleconosco'] = 'Contact Us';

// Custom HTML settings
$string['customhtmlsettings'] = 'Custom HTML Settings';
$string['custom_html_title'] = 'Custom HTML';
$string['custom_html'] = 'Custom HTML code';
$string['custom_html_desc'] = 'Insert custom HTML code that will be added to the end of the page. Ideal for chat scripts, analytics or other third-party codes. Use with caution and make sure the code is safe.';
$string['custom_html_logged_only'] = 'Display only for logged users';
$string['custom_html_logged_only_desc'] = 'If enabled, the custom HTML code will be displayed only for authenticated users (not for visitors or login page).';