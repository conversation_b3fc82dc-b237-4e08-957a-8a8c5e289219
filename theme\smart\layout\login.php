<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

defined('MOODLE_INTERNAL') || die();

/**
 * A login page layout for the smart theme.
 *
 * @package   theme_smart
 * @copyright 2016 Damyon Wiese
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$bodyattributes = $OUTPUT->body_attributes();

$templatecontext = [
	'sitename' => format_string($SITE->shortname, true, ['context' => context_course::instance(SITEID), "escape" => false]),
	'output' => $OUTPUT,
	'bodyattributes' => $bodyattributes
];

// Adicionar HTML personalizado ao contexto (para página de login, só se não estiver configurado para usuários logados apenas)
$OUTPUT->smart_custom_html = $OUTPUT->smart_custom_html();

echo $OUTPUT->render_from_template('theme_smart/login', $templatecontext);


if (strpos($PAGE->url->out(), '/login/verify_age_location.php') !== false) {
	echo '
			<script>

					var form = document.querySelector(\'form\');
					var inputs = form.querySelectorAll(\'input\'); 
					
					////////////////////////////
					// Definição das variaveis
					////////////////////////////

					var ageInput = form.querySelector(\'input[name="age"]\');
					var countryInput = form.querySelector(\'select[name="country"]\');
					
					

					function createHoverListener(input, labelText) {
						return function() {
								input.title = labelText;
						};
					}

						
						createHoverListener(ageInput, "Campo Obrigatório.")();
						createHoverListener(countryInput, "Campo Obrigatório.")();


					ageInput.addEventListener("focus", createFocusListener(ageInput, "Campo Obrigatório,Qual a sua idade?"));
					countryInput.addEventListener("focus", createFocusListener(countryInput, "Campo Obrigatório, Em qual país voê mora?"));
			</script>
	';
}


if (strpos($PAGE->url->out(), '/login/signup.php') !== false) {
	echo '
		<script>

			document.addEventListener("DOMContentLoaded", function() {
    // Adiciona tabIndex="-1" ao campo form-control-static
    var formControlStatic = document.querySelector(".form-control-static");
    if (formControlStatic && formControlStatic.getAttribute("tabIndex") !== "-1") {
        formControlStatic.setAttribute("tabIndex", "-1");
    }

    // Exclui form-control-feedback e invalid-feedback da ordem de tabulação
    var excludedElements = document.querySelectorAll(".form-control-feedback, .invalid-feedback");
    excludedElements.forEach(function(element) {
        if (element.getAttribute("tabIndex") !== "-1") {
            element.setAttribute("tabIndex", "-1");
        }
    });

    // Adiciona evento blur aos inputs
    var inputs = document.querySelectorAll("input");
    inputs.forEach(function(input) {
        input.addEventListener("blur", function() {
            var fieldsToShow = document.querySelectorAll(".fields-to-show");
            fieldsToShow.forEach(function(field) {
                field.setAttribute("tabIndex", "-1");
            });
        });
    });

    // Adiciona aria-live a todos os inputs
    var inputs = document.querySelectorAll("input");
    inputs.forEach(function(input) {
        input.setAttribute("aria-live", "polite");
        input.addEventListener("focus", function() {
            speak("Campo Obrigatório");
        });
    });

    function speak(text) {
        var liveRegion = document.getElementById("live-region");
        if (!liveRegion) {
            liveRegion = document.createElement("div");
            liveRegion.id = "live-region";
            liveRegion.style.cssText = "position: absolute; left: -9999px; top: -9999px;";
            document.body.appendChild(liveRegion);
        }

        liveRegion.textContent = text;
        setTimeout(function() {
            liveRegion.textContent = "";
        }, 1000);
    }
});


		</script>
	';
}
