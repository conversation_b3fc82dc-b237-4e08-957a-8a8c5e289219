<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace theme_smart\output;

use core_useragent;
use local_multithemes\utils\theme;
use moodle_url;
use html_writer;
use custom_menu;
use get_string;
use paging_bar;

defined('MOODLE_INTERNAL') || die;

/**
 * Renderers to align <PERSON>odle's HTML with that expected by <PERSON><PERSON><PERSON>
 *
 * @package    theme_smart
 * @copyright  2012 Bas Brands, www.basbrands.nl
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class core_renderer extends \core_renderer
{

    /**
     * Returns HTML to display a single paging bar to provide access to other pages  (usually in a search)
     *
     * Theme developers: DO NOT OVERRIDE! Please override function
     * {@link core_renderer::render_paging_bar()} instead.
     *
     * @param int $totalcount The total number of entries available to be paged through
     * @param int $page The page you are currently viewing
     * @param int $perpage The number of entries that should be shown per page
     * @param string|moodle_url $baseurl url of the current page, the $pagevar parameter is added
     * @param string $pagevar name of page parameter that holds the page number
     * @return string the HTML to output.
     */
    public function paging_bar($totalcount, $page, $perpage, $baseurl, $pagevar = 'page')
    {
        $pb = new spark_paging_bar($totalcount, $page, $perpage, $baseurl, $pagevar);
        return $this->render($pb);
    }


    /**
     * Returns HTML to display the paging bar.
     *
     * @param paging_bar $pagingbar
     * @return string the HTML to output.
     */
    protected function render_paging_bar(paging_bar $pagingbar)
    {
        $pb = new spark_paging_bar($pagingbar->totalcount, $pagingbar->page, $pagingbar->perpage, $pagingbar->baseurl, $pagingbar->pagevar);

        if ($pb->maxdisplay > 10) {
            $pb->maxdisplay = 10;
        }

        return $this->render_from_template('core/paging_bar', $pb->export_for_template($this));
    }
    /**
     * Wrapper for header elements.
     *
     * @return string HTML to display the main header.
     */
    public function full_header()
    {
        global $OUTPUT, $PAGE, $USER;

        $pagetype = $this->page->pagetype;
        $homepage = get_home_page();
        $homepagetype = null;
        // Add a special case since /my/courses is a part of the /my subsystem.
        if ($homepage == HOMEPAGE_MY || $homepage == HOMEPAGE_MYCOURSES) {
            $homepagetype = 'my-index';
        } else if ($homepage == HOMEPAGE_SITE) {
            $homepagetype = 'site-index';
        }
        if (
            $this->page->include_region_main_settings_in_header_actions() &&
            !$this->page->blocks->is_block_present('settings')
        ) {
            // Only include the region main settings if the page has requested it and it doesn't already have
            // the settings block on it. The region main settings are included in the settings block and
            // duplicating the content causes behat failures.
            $this->page->add_header_action(html_writer::div(
                $this->region_main_settings_menu(),
                'd-print-none',
                ['id' => 'region-main-settings-menu']
            ));
        }

        $primary = new \core\navigation\output\primary($PAGE);
        $primarymenu = $this->primary_menu($primary->export_for_template($this));

        $header = new \stdClass();
        $header->home_url = new moodle_url(theme_smart_get_home_page());
        $header->settingsmenu = $this->context_header_settings_menu();
        $header->contextheader = $this->context_header();
        $header->hasnavbar = empty($this->page->layout_options['nonavbar']);
        $header->navbar = $this->navbar();
        $header->pageheadingbutton = $this->page_heading_button();
        $header->courseheader = $this->course_header();
        $header->headeractions = $this->page->get_header_actions();
        $header->has_headeractions = $header->headeractions ? true : false;
        $header->global_search_box = $this->global_search_box();
        $header->is_course_page = $this->page->pagelayout == "course" || false;
        $header->usermenu = $primarymenu['user'];
        $header->output = $OUTPUT;

        if (!empty($pagetype) && !empty($homepagetype) && $pagetype == $homepagetype) {
            $header->welcomemessage = \core_user::welcome_message();
        }

        return $this->render_from_template('core/full_header', $header);
    }

    /**
     * Renders the login form.
     *
     * @param \core_auth\output\login $form The renderable.
     * @return string
     */
    public function render_login(\core_auth\output\login $form)
    {
        global $CFG, $SITE, $OUTPUT;
        $theme = \theme_config::load('smart');

        $context = $form->export_for_template($this);

        $context->errorformatted = $this->error_text($context->error);
        //$url = $this->get_logo_url();
        //if ($url) {
        //    $url = $url->out(false);
        //}
        $context->logourl = $theme->setting_file_url('loginlogo', 'loginlogo') ?? $OUTPUT->image_url('logo-full', 'theme');
        $context->sitename = format_string(
            $SITE->fullname,
            true,
            ['context' => \context_course::instance(SITEID), "escape" => false]
        );

        return $this->render_from_template('core/loginform', $context);
    }

    public function course_headeractions()
    {
        if (!$this->page->get_header_actions()) {
            return null;
        }

        return (object)[
            "headeractions" => $this->page->get_header_actions(),
        ];
    }

    public function edit_button(moodle_url $url, string $method = 'post')
    {
        if ($this->page->theme->haseditswitch) {
            return;
        }
        $url->param('sesskey', sesskey());
        if ($this->page->user_is_editing()) {
            $url->param('edit', 'off');
            $editstring = get_string('turneditingoff');
        } else {
            $url->param('edit', 'on');
            $editstring = get_string('turneditingon');
        }
        $button = new \single_button($url, $editstring, $method, ['class' => 'btn btn-primary']);
        return $this->render_single_button($button);
    }

    /**
     * Returns a global search box.
     *
     * @param  string $id     The search box wrapper div id, defaults to an autogenerated one.
     * @return string         HTML with the search form hidden by default.
     */
    public function global_search_box($id = false)
    {
        global $CFG;

        // Accessing $CFG directly as using \core_search::is_global_search_enabled would
        // result in an extra included file for each site, even the ones where global search
        // is disabled.
        if (empty($CFG->enableglobalsearch) || !has_capability('moodle/search:query', \context_system::instance())) {
            return '';
        }

        $data = [
            'action' => new moodle_url('/search/index.php'),
            'hiddenfields' => (object) ['name' => 'context', 'value' => $this->page->context->id],
            'inputname' => 'q',
            'searchstring' => get_string('search'),
        ];

        return $data;
    }

    /**
     * Renders the "breadcrumb" for all pages in smart.
     *
     * @return string the HTML for the navbar.
     */
    //public function navbar(): string {
    //    $newnav = new \theme_smart\smartnavbar($this->page);
    //    return $this->render_from_template('core/navbar', $newnav);
    //}

    public function primary_menu($menu)
    {
        if (!isset($menu["user"]["items"])) {
            return $menu;
        }

        foreach ($menu["user"]["items"] as $key => $item) {
            if (
                (isset($item->titleidentifier) && strstr($item->titleidentifier, "preferences"))
                || (isset($item->itemtype) && $item->itemtype == "submenu-link")
                || (isset($item->itemtype) && $item->itemtype == "divider")
            ) {
                unset($menu["user"]["items"][$key]);
            }
        }

        $menu["user"]["items"] = array_values($menu["user"]["items"]);

        return $menu;
    }

    public function navbar()
    {
        $navbaritems = $this->page->navbar->get_items();

        $breadcrumbitems = $this->filter_course_breadcrumbs($navbaritems);

        return $this->render_from_template('core/navbar', [
            'home_page' => new moodle_url(theme_smart_get_home_page()),
            "get_items" => $breadcrumbitems
        ]);
    }

    public function filter_course_breadcrumbs($items)
    {
        global $PAGE;

        $entryname = optional_param('entryname', NULL, PARAM_TEXT);
        $entryurl = optional_param('entryurl', NULL, PARAM_URL);

        $breadcrumbitems = $items;
        $hascourserootnode = false;

        foreach ($items as $item) {
            $isrootnode = $item->type == \navigation_node::TYPE_ROOTNODE;
            $iscourseroot = $isrootnode && $item->get_content() == get_string('courses');

            if ($item->key == "myhome" || (isguestuser() && $item->key == "home")) {
                $item->display = false;
            }

            if (mb_strtolower($item->get_content()) == mb_strtolower(get_string('mycourses')) && $entryname && $entryurl) {
                switch ($entryname) {
                    case 'local_catalog':
                        $text = get_string('pluginname', 'local_catalog');
                        break;
                }

                $item->text = $text;
                $item->action = new moodle_url($entryurl);
            }

            if ($iscourseroot) {
                $hascourserootnode = true;
                break;
            }
        }

        if ($hascourserootnode) {
            $breadcrumbitems = array_filter($breadcrumbitems, function ($item) {
                $isrootnode = $item->type == \navigation_node::TYPE_ROOTNODE;
                $iscategorynode = $item->type == \navigation_node::TYPE_CATEGORY;
                $iscourseroot = $isrootnode && $item->get_content() == get_string('courses');

                return (!$iscourseroot) && (!$iscategorynode);
            });

            $breadcrumbitems = array_values($breadcrumbitems);
        }

        return $breadcrumbitems;
    }



    /**
     * Renders the context header for the page.
     *
     * @param array $headerinfo Heading information.
     * @param int $headinglevel What 'h' level to make the heading.
     * @return string A rendered context header.
     */
    public function context_header($headerinfo = null, $headinglevel = 1): string
    {
        global $DB, $USER, $CFG;
        require_once($CFG->dirroot . '/user/lib.php');
        $context = $this->page->context;
        $heading = null;
        $imagedata = null;
        $userbuttons = null;

        // Make sure to use the heading if it has been set.
        if (isset($headerinfo['heading'])) {
            $heading = $headerinfo['heading'];
        } else {
            $heading = $this->page->heading;
        }

        // The user context currently has images and buttons. Other contexts may follow.
        if ((isset($headerinfo['user']) || $context->contextlevel == CONTEXT_USER) && $this->page->pagetype !== 'my-index') {
            if (isset($headerinfo['user'])) {
                $user = $headerinfo['user'];
            } else {
                // Look up the user information if it is not supplied.
                $user = $DB->get_record('user', array('id' => $context->instanceid));
            }

            // If the user context is set, then use that for capability checks.
            if (isset($headerinfo['usercontext'])) {
                $context = $headerinfo['usercontext'];
            }

            // Only provide user information if the user is the current user, or a user which the current user can view.
            // When checking user_can_view_profile(), either:
            // If the page context is course, check the course context (from the page object) or;
            // If page context is NOT course, then check across all courses.
            $course = ($this->page->context->contextlevel == CONTEXT_COURSE) ? $this->page->course : null;

            if (user_can_view_profile($user, $course)) {
                // Use the user's full name if the heading isn't set.
                if (empty($heading)) {
                    $heading = fullname($user);
                }

                $imagedata = $this->user_picture($user, array('size' => 100));

                // Check to see if we should be displaying a message button.
                if (!empty($CFG->messaging) && has_capability('moodle/site:sendmessage', $context)) {
                    $userbuttons = array(
                        'messages' => array(
                            'buttontype' => 'message',
                            'title' => get_string('message', 'message'),
                            'url' => new moodle_url('/message/index.php', array('id' => $user->id)),
                            'image' => 'message',
                            'linkattributes' => \core_message\helper::messageuser_link_params($user->id),
                            'page' => $this->page
                        )
                    );

                    if ($USER->id != $user->id) {
                        $iscontact = \core_message\api::is_contact($USER->id, $user->id);
                        $contacttitle = $iscontact ? 'removefromyourcontacts' : 'addtoyourcontacts';
                        $contacturlaction = $iscontact ? 'removecontact' : 'addcontact';
                        $contactimage = $iscontact ? 'removecontact' : 'addcontact';
                        $userbuttons['togglecontact'] = array(
                            'buttontype' => 'togglecontact',
                            'title' => get_string($contacttitle, 'message'),
                            'url' => new moodle_url(
                                '/message/index.php',
                                array(
                                    'user1' => $USER->id,
                                    'user2' => $user->id,
                                    $contacturlaction => $user->id,
                                    'sesskey' => sesskey()
                                )
                            ),
                            'image' => $contactimage,
                            'linkattributes' => \core_message\helper::togglecontact_link_params($user, $iscontact),
                            'page' => $this->page
                        );
                    }

                    $this->page->requires->string_for_js('changesmadereallygoaway', 'moodle');
                }
            } else {
                $heading = null;
            }
        }

        $prefix = null;
        if ($context->contextlevel == CONTEXT_MODULE) {
            if ($this->page->course->format === 'singleactivity') {
                $heading = format_string($this->page->course->fullname, true, ['context' => $context]);
            } else {
                $heading = $this->page->cm->get_formatted_name();
                $iconurl = $this->page->cm->get_icon_url();
                $iconclass = $iconurl->get_param('filtericon') ? '' : 'nofilter';
                $iconattrs = [
                    'class' => "icon activityicon $iconclass",
                    'aria-hidden' => 'true'
                ];
                $imagedata = html_writer::img($iconurl->out(false), '', $iconattrs);
                $purposeclass = plugin_supports('mod', $this->page->activityname, FEATURE_MOD_PURPOSE);
                $purposeclass .= ' activityiconcontainer';
                $purposeclass .= ' modicon_' . $this->page->activityname;
                $imagedata = html_writer::tag('div', $imagedata, ['class' => $purposeclass]);
                if (!empty($USER->editing)) {
                    $prefix = get_string('modulename', $this->page->activityname);
                }
            }
        }

        $contextheader = new \context_header($heading, $headinglevel, $imagedata, $userbuttons, $prefix);
        return $this->render_context_header($contextheader);
    }

    /**
     * Renders the header bar.
     *
     * @param context_header $contextheader Header bar object.
     * @return string HTML for the header bar.
     */
    protected function render_context_header(\context_header $contextheader)
    {

        // Generate the heading first and before everything else as we might have to do an early return.
        if (!isset($contextheader->heading)) {
            $heading = $this->heading($this->page->heading, $contextheader->headinglevel, 'h2');
        } else {
            $heading = $this->heading($contextheader->heading, $contextheader->headinglevel, 'h2');
        }

        // All the html stuff goes here.
        $html = html_writer::start_div('page-context-header');

        // Image data.
        if (isset($contextheader->imagedata)) {
            // Header specific image.
            $html .= html_writer::div($contextheader->imagedata, 'page-header-image mr-2');
        }

        // Headings.
        if (isset($contextheader->prefix)) {
            $prefix = html_writer::div($contextheader->prefix, 'text-muted text-uppercase small line-height-3');
            $heading = $prefix . $heading;
        }
        $html .= html_writer::tag('div', $heading, array('class' => 'page-header-headings'));

        // Buttons.
        if (isset($contextheader->additionalbuttons)) {
            $html .= html_writer::start_div('btn-group header-button-group');
            foreach ($contextheader->additionalbuttons as $button) {
                if (!isset($button->page)) {
                    // Include js for messaging.
                    if ($button['buttontype'] === 'togglecontact') {
                        \core_message\helper::togglecontact_requirejs();
                    }
                    if ($button['buttontype'] === 'message') {
                        \core_message\helper::messageuser_requirejs();
                    }
                    $image = $this->pix_icon($button['formattedimage'], $button['title'], 'moodle', array(
                        'class' => 'iconsmall',
                        'role' => 'presentation'
                    ));
                    $image .= html_writer::span($button['title'], 'header-button-title');
                } else {
                    $image = html_writer::empty_tag('img', array(
                        'src' => $button['formattedimage'],
                        'role' => 'presentation'
                    ));
                }
                $html .= html_writer::link($button['url'], html_writer::tag('span', $image), $button['linkattributes']);
            }
            $html .= html_writer::end_div();
        }
        $html .= html_writer::end_div();

        return $html;
    }

    /**
     * See if this is the first view of the current cm in the session if it has fake blocks.
     *
     * (We track up to 100 cms so as not to overflow the session.)
     * This is done for drawer regions containing fake blocks so we can show blocks automatically.
     *
     * @return boolean true if the page has fakeblocks and this is the first visit.
     */
    public function firstview_fakeblocks(): bool
    {
        global $SESSION;

        $firstview = false;
        if ($this->page->cm) {
            if (!$this->page->blocks->region_has_fakeblocks('side-pre')) {
                return false;
            }
            if (!property_exists($SESSION, 'firstview_fakeblocks')) {
                $SESSION->firstview_fakeblocks = [];
            }
            if (array_key_exists($this->page->cm->id, $SESSION->firstview_fakeblocks)) {
                $firstview = false;
            } else {
                $SESSION->firstview_fakeblocks[$this->page->cm->id] = true;
                $firstview = true;
                if (count($SESSION->firstview_fakeblocks) > 100) {
                    array_shift($SESSION->firstview_fakeblocks);
                }
            }
        }
        return $firstview;
    }

    public function get_theme_logo_url()
    {
        global $OUTPUT;
        return $OUTPUT->image_url('logo', 'theme');
    }

    public function get_theme_main_logo_url()
    {
        global $OUTPUT;
        return $OUTPUT->image_url('logo-full', 'theme');
    }

    public function menubar()
    {
        $menubar = (object)[
            "menuitems" => $this->menubar_items(),
        ];

        return $menubar;
    }

    public function menubar_items()
    {
        global $USER, $CFG, $OUTPUT, $PAGE;

        $menu = [];

        if (!isloggedin()) {
            return $menu;
        }

        // Home
        $menu[] = (object)[
            "name" => get_string("home"),
            "icon" => "icon-home",
            "url" => new \moodle_url("/my"),
            "active" => $this->check_active_item("mydashboard"),
        ];

        // My Courses (fallback to the default courses page in case block_mycourse does not exists)
        if (!array_key_exists('mycourses', \core\plugininfo\block::get_enabled_plugins()) && !isguestuser()) {
            $menu[] = (object)[
                "name" => get_string("mycourses"),
                "icon" => "icon-courses",
                "url" => new \moodle_url("/my/courses.php"),
                "active" => $this->check_active_item("mycourses"),
            ];
        }

        // Judy
        if (get_config('theme_smart', 'enable_ai_menu') && (get_config('theme_smart', 'ai_assistantid'))) {
            $menu[] = (object)[
                "name"     => '',
                "class"    => 'd-flex d-md-none d-md-flex d-lg-none open-judy',
                "pix_icon" => $this->image_icon('fp/judy', 'Judy', 'theme'),
                "url"      => 'javascript:;',
            ];
        }

        // Categories
        if(!get_config('local_catalog', 'enableplugin')){
            $menu[] = (object)[
                "name" => get_string("catalog", "theme_smart"),
                "icon" => "icon-category",
                "url" => new \moodle_url("/course/index.php"),
                "active" => $this->check_active_item("categories"),
            ];
        }

        // Ranking
        if (get_config("block_xp", "enableladder") && !isguestuser()) {
            $menu[] = (object)[
                "name" => get_string("ranking", "theme_smart"),
                "icon" => "icon-trophy",
                "url" => new \moodle_url("/blocks/xp/index.php/ladder/1"),
                "active" => $this->check_active_item("ranking"),
            ];
        }

        // Dashboard
        if (has_capability('report/dashboard:view', \context_system::instance())) {
            $menu[] = (object)[
                "name" => get_string("pluginname", "report_dashboard"),
                "icon" => "fa fa-line-chart fa-fw",
                "url" => new \moodle_url("/report/dashboard/index.php"),
                "active" => $PAGE->pagetype == "report-dashboard-index" ? 'active' : false,
            ];
        }

        // Calendar
        $menu[] = (object)[
            "name" => get_string("calendar", "calendar"),
            "icon" => "icon-calendar",
            "url" => new \moodle_url("/calendar/view.php?view=month"),
            "active" => $this->check_active_item("calendar"),
        ];

        // Fale Conosco
        $menu[] = (object)[
            "name" => get_string("faleconosco", "theme_smart"),
            "icon" => "fas fa-headset",
            "url" => "https://epicbrasil.atlassian.net/servicedesk/customer/portal/122",
            "active" => "",
            "target" => "_blank",
            "order" => 9
        ];

        // If you need to show a plugin icon in the menu bar
        // be sure that the function "plugin_pluginname_add_menubar_icon" is in your plugin lib.php
        // That function must return an object with the following properties:
        // [name: string, icon: css class (fontawesome or similar), url: moodle_url, active: string (active || ''), order: int || null]

        $pluginsicon = get_plugins_with_function('add_menubar_icon', 'lib.php');

        if ($pluginsicon) {
            foreach ($pluginsicon as $plugintype => $plugins) {
                foreach ($plugins as $pluginfunction) {
                    $menunode = $pluginfunction($this);
                    if ($menunode !== false) {
                        $menu[] = $menunode;
                    }
                }
            }
        }

        $reordered_menu = [];

        foreach ($menu as $item) {
            if (!isset($item->order) || !$item->order) {
                $reordered_menu[] = $item;
                continue;
            }

            $item->order = $item->order < 0 ? 0 : $item->order;

            if (is_numeric($item->order) && isset($reordered_menu[$item->order])) {
                array_splice($reordered_menu, $item->order, 0, [$item]);
            } else {
                $reordered_menu[$item->order] = $item;
            }
        }

        foreach (array_values($reordered_menu) as $i => &$item) {
            $item->index = $i;
        }

        // Menu de Administration para administradores e clientes regulares
        if ((has_capability('moodle/site:configview', \context_system::instance()) || theme_smart_is_client()) &&
            !(theme_smart_has_role('editorcursos') || theme_smart_has_role('coordenador'))) {

            $adminmenu_name = get_string('administrationsite', 'moodle');
            $client_admin_submenu = null;
            $item_active = $this->check_active_item("admin");
            $menu_url = '/admin/search.php'; // URL padrão para o Site Administration

            if (theme_smart_is_client() && !is_siteadmin()) {
                $adminmenu_name = get_string('adminmenu_name', 'theme_smart');
                $clientsubmenu = get_plugins_with_function('add_menubar_client_menu', 'lib.php');

                if ($clientsubmenu) {
                    $client_admin_submenu = [];

                    foreach ($clientsubmenu as $plugintype => $plugins) {
                        foreach ($plugins as $pluginfunction) {
                            $menunode = $pluginfunction($this);
                            if ($menunode !== false) {
                                $client_admin_submenu[] = $menunode;
                            }
                        }
                    }
                }

                $item_active = array_search('active', array_column($client_admin_submenu, 'active')) !== false ? "active" : "";
                $menu_url = '#'; // Para clientes, usar # para abrir o menu em vez de redirecionar
            }

            $reordered_menu[] = (object)[
                "divider" => true,
                "name" => $adminmenu_name,
                "icon" => "fa-solid fa-sliders",
                "class" => "menu-admin",
                "url" => new moodle_url($menu_url),
                "active" => $item_active,
                "submenu" => $client_admin_submenu,
                "has_submenu" => !empty($client_admin_submenu)
            ];
        }

        // Menu de Administration específico para Coordenador e Editor de Cursos
        if (theme_smart_has_role('editorcursos') || theme_smart_has_role('coordenador')) {
            $adminmenu_name = get_string('adminmenu_name', 'theme_smart');
            $item_active = $this->check_active_item_by_url("admin/tool/coursemanagement");

            // Criar o submenu com apenas a opção de Gerenciador de Cursos
            $custom_admin_submenu = [];

            // Adicionar o item de Gerenciador de Cursos
            $custom_admin_submenu[] = (object)[
                "name" => get_string('menubarname', 'tool_coursemanagement'),
                "url" => new \moodle_url("/admin/tool/coursemanagement/index.php"),
                "active" => $item_active
            ];

            $reordered_menu[] = (object)[
                "divider" => true,
                "name" => $adminmenu_name,
                "icon" => "fa-solid fa-sliders",
                "class" => "menu-admin",
                "url" => '#', // Usar # para garantir que o menu seja aberto ao clicar, em vez de redirecionar
                "active" => $item_active,
                "submenu" => $custom_admin_submenu,
                "has_submenu" => true
            ];
        }

        return array_values($reordered_menu);
    }

    public function check_active_item($name)
    {
        global $PAGE;

        if ($PAGE->pagelayout == $name) {
            return "active";
        }

        return '';
    }

    public function check_active_item_by_url($url)
    {
        global $PAGE;

        if (strstr($PAGE->url, $url)) {
            return "active";
        }

        return '';
    }

    /**
     * Returns the language menu if one has been set
     *
     * @return string
     */
    public function smart_lang_menu()
    {
        global $CFG;

        $langmenu = new custom_menu();
        return $this->smart_render_lang_menu($langmenu);
    }

    /**
     * Return user avatar if available
     *
     * @return string
     */
    public function smart_get_avatar_url()
    {
        global $USER, $PAGE;

        $pic = new \user_picture($USER);
        $pic->size = 1;

        return $pic->get_url($PAGE);
    }

    /**
     * Returns the current language flag image
     *
     * @return string
     */
    public function smart_get_current_lang_flag()
    {
        global $OUTPUT;

        $currentlang = current_language();
        return $OUTPUT->image_url("lang/" . $currentlang, 'theme');
    }

    /*
     * This renders the customized bootstrap top language menu.
     *
     * This renderer is needed to enable the Bootstrap style navigation.
     */
    protected function smart_render_lang_menu(custom_menu $menu)
    {
        global $CFG, $OUTPUT;

        $langs = get_string_manager()->get_list_of_translations();
        $haslangmenu = $this->lang_menu() != '';

        if (!$haslangmenu) {
            return '';
        }

        if ($haslangmenu) {
            $strlang = get_string('language');
            $currentlang = current_language();
            $currentflag = $OUTPUT->image_url("lang/" . $currentlang, 'theme');

            if (isset($langs[$currentlang])) {
                $thislang = $langs[$currentlang];
                preg_match('#\((.*?)\)#', $thislang, $thislangtype);
                $currentlang = preg_replace("/\([^)]+\)/", "", $thislang);
                $currentlangtype = $thislangtype[1];
            } else {
                $currentlang = $strlang;
            }

            $currentlang = strstr($currentlang, "English") ? $currentlang . "- US" : $currentlang;
            $this->language = $menu->add($currentlang, new moodle_url('#'), $strlang, 10000);

            foreach ($langs as $langtype => $lang) {
                $langname = preg_replace("/\([^)]+\)/", "", $lang);
                $langname = strstr($langname, "English") ? $langname . "- US" : $langname;
                $langtypes[] = $langtype;
                $this->language->add($langname, new moodle_url($this->page->url, array('lang' => $langtype)), $langname);
            }
        }

        $content = '';

        foreach ($menu->get_children() as $item) {
            $context           = $item->export_for_template($this);
            $context->haslang  = true;
            $context->key      = $currentlangtype;
            $context->flag     = $currentflag;

            for ($l = 0; $l < count($langs); $l++) {
                $context->children[$l]->key = $langtypes[$l];
                $context->children[$l]->flag = $OUTPUT->image_url("lang/" . $langtypes[$l], 'theme');
            }

            $content .= $this->render_from_template('theme_smart/language_menu_item', $context);
        }

        return $content;
    }

    public function show_edit_switch()
    {
        return is_siteadmin();
    }

    public function smart_edit_button()
    {
        if (!$this->page->user_allowed_editing()) {
            return null;
        }

        $url = $this->page->url;
        $url->param('sesskey', sesskey());
        $url->param('pagecontextid', $this->page->context->id);

        if ($this->page->user_is_editing()) {
            $url->param('edit', 'off');
            $icon = "fa fa-lock-open";
            $editstring = get_string('turneditingoff');
        } else {
            $url->param('edit', 'on');
            $icon = "fa fa-lock";
            $editstring = get_string('turneditingon');
        }

        $data = (object)[
            "url" => $url,
            "icon" => $icon,
            "editstring" => $editstring,
        ];

        return $data;
    }

    public function smart_footer()
    {
        global $CFG, $OUTPUT, $PAGE, $USER;

        $theme_options = (object)$PAGE->theme->layouts[$PAGE->pagelayout];
        $footerType = isset($theme_options->options["footer"]) ? $theme_options->options["footer"] : "simple";
        $theme = \theme_config::load('smart');
        $device = \core_useragent::get_device_type();

        $multitheme = [];

        $pluginversion = get_config('local_multithemes','version');

        if ($USER->id) {
            $multitheme = $pluginversion >= 2024060300
                ? theme::get_user_theme($USER->id)
                : theme::get_system_theme()
            ;
        }
        // Site Map
        $menuitems = $this->menubar_items();

        if (!isguestuser()) {
            $menuitems[] = (object)[
                "name" => get_string("messages", "message"),
                "url" => new \moodle_url("/message/index.php"),
            ];
        }

        if (!isguestuser()) {
            $menuitems[] = (object)[
                "name" => get_string("notifications", "message"),
                "url" => new \moodle_url("/message/output/popup/notifications.php"),
            ];
        }

        $sitemap = array_chunk($menuitems, ceil(count($menuitems) / 2));

        // Social Links
        $social = isset($multitheme->social) ? $multitheme->social : [];

        // Mobile Apps
        $apps = isset($multitheme->apps) ? $multitheme->apps : [];

        $enableMustachesFooter = (bool) get_config('theme_smart', 'enable_mustaches_footer');
        $footerHiden = (bool) get_config('theme_smart', 'footerHiden');

        $dataFooter = (object)[
            "hide_footer" => $footerType === false || false,
            "footer_full" => $footerType == "full" || false,
            "footer_simple" => $footerType == "simple" || false,
            "footerlogo" => isset($multitheme->footerlogo) ? $multitheme->footerlogo : '',
            "address" => isset($multitheme->companyaddress) ? nl2br($multitheme->companyaddress) : "",
            "sitemap" => ["col1" => $sitemap[0], "col2" => $sitemap[1]],
            "social" => $social,
            "has_social" => !empty($social),
            "has_apps" => !empty($apps),
            "apps" => $apps,
            "language_menu" => $this->smart_lang_menu(),
            "privacy_url" => get_config("moodle", "sitepolicy"),
            "terms_url" => "#",
            "credits_logo" => $OUTPUT->image_url("revvo", 'theme'),
            "has_admin" => $this->is_siteadmin(),
            "enable_mustaches_footer" => $enableMustachesFooter,
            "footerHiden" => $footerHiden,
            "enable_login_footer" => (bool) get_config('theme_smart', 'enable_login_footer'),
        ];

        if (get_config('theme_smart', 'enable_ai_menu') && get_config('theme_smart', 'ai_assistantid') && $USER->id) {
            $excluded_pages = [
                'report-dashboard-index'
            ];

            if (!in_array($this->page->pagetype, $excluded_pages)) {
                $dataFooter->enable_judy = true;

                if ($device == \core_useragent::DEVICETYPE_MOBILE) {
                    $dataFooter->ai_trigger_type = "click";
                } else {
                    $dataFooter->ai_trigger_type = "auto";
                }

                $dataFooter->userAvatarUrl = $this->smart_get_avatar_url();
                $dataFooter->ai_assistant_id = get_config('theme_smart', 'ai_assistantid');
            }
        }

        return $dataFooter;
    }

    public function is_siteadmin()
    {
        return is_siteadmin();
    }

    /**
     * Renderiza HTML personalizado configurado no tema
     *
     * @return string HTML personalizado ou string vazia
     */
    public function smart_custom_html()
    {
        global $USER;

        $customHtml = get_config('theme_smart', 'custom_html');
        $loggedOnly = get_config('theme_smart', 'custom_html_logged_only');

        // Se não há HTML personalizado configurado, retorna vazio
        if (empty($customHtml)) {
            return '';
        }

        // Se está configurado para mostrar apenas para usuários logados
        if ($loggedOnly && (!$USER || !$USER->id || isguestuser())) {
            return '';
        }

        // Retorna o HTML personalizado
        return $customHtml;
    }
}
