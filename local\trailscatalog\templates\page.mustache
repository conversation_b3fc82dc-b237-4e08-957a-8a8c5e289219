<div class="trailscatalog-page">
    <div class="trailscatalog-header">
        {{#page}}
            <div>
                <h2>{{ title }}</h2>
                <h5 class="text-secondary font-weight-light">{{{ description }}}</h5>
            </div>

            <div class="actions">
                {{#returnurl}}
                    <a href="{{ returnurl }}" class="back-button btn btn-dark rounded-pill">
                        <i class="fa-solid fa-arrow-left mr-2"></i> {{#str}} back, local_trailscatalog {{/str}}
                    </a>
                {{/returnurl}}

                {{#additionaloptions}}
                    <div class="ml-auto d-flex">
                        <div class="navitem dropdown">
                            <button aria-label="{{#str}}moreactions,local_trailscatalog{{/str}}" class="btn btn-dark rounded-circle ml-auto" id="dropdown-actions" data-toggle="dropdown" aria-haspopup="true" aria-controls="moreactionsmenu">
                                <i class="fa fa-ellipsis-v fa-fw" title="Menu" data-toggle="tooltip" data-placement="bottom" data-trigger="hover"></i>
                            </button>

                            <div id="moreactionsmenu" class="dropdown-menu dropdown-menu-right mt-2" aria-labelledby="dropdown-actions" role="menu">
                                {{#options}}
                                    <a role="menuitem" class="dropdown-item" {{#attributes}}{{name}}="{{value}}"{{/attributes}} href="{{url}}" tabindex="-1">{{string}}</a>
                                {{/options}}
                            </div>
                        </div>
                    </div>
                {{/additionaloptions}}
            </div>
        {{/page}}
    </div>
    {{#filters}}
        <div class="container-fluid tertiary-navigation mt-2 mb-5" id="action_bar">
            <div class="row d-block d-md-flex">
                {{#categoryselect}}
                    <div class="navitem flex-column mb-0 col-md-4 col-xl-3 p-0">
                        <label>{{#str}} course_category, local_trailscatalog {{/str}}</label>
                        {{> core/url_select }}
                    </div>
                {{/categoryselect}}
                {{#search}}
                    <div class="navitem flex-column mb-0 col p-0 mt-2 mt-md-0">
                        <label>{{#str}} search, local_trailscatalog {{/str}}</label>
                        <div class="simplesearchform d-flex">
                            <form autocomplete="off" action="{{action}}" method="get" accept-charset="utf-8" class="mform form-inline simplesearchform">
                                <div class="input-group">
                                    <label for="searchinput-{{uniqid}}">
                                    <span class="sr-only">{{#str}} search, core {{/str}}</span>
                                    </label>
                                    <input type="text"
                                        id="searchinput-{{uniqid}}"
                                        class="form-control"
                                        placeholder="{{#str}} search, core {{/str}}"
                                        aria-label="{{#str}} search, core {{/str}}"
                                        name="search"
                                        data-region="input"
                                        autocomplete="off"
                                        {{#query}}value="{{.}}"{{/query}}
                                    >
                                    <div class="input-group-append">
                                    <button type="submit" class="btn btn-primary search-icon">
                                        {{#pix}} a/search, core {{/pix}}
                                        <span class="sr-only">{{#str}} search, core {{/str}}</span>
                                    </button>
                                    </div>
                                </div>
                                <input type="hidden" name="categoryid" value="{{categoryid}}">
                            </form>
                        </div>
                    </div>
                {{/search}}
                {{#has_categories}}
                <div class="justify-content-end ml-auto mt-3 mt-md-auto text-right">
                    <div class="btn-group" role="group" aria-label="Visualizar como...">
                        <a href="?categoryid={{search.categoryid}}&view=cards" class="btn-view btn-cards-view {{#view.cards}}active{{/view.cards}} btn btn-dark">
                            <span class="pr-1" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-grid-fill" viewBox="0 0 16 16">
                                    <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5zm8 0A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5zm-8 8A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5zm8 0A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5z"/>
                                </svg>
                            </span>
                            <span style="padding-top: 0.3rem;">{{#str}}cards,local_trailscatalog{{/str}}</span>
                            {{#view.cards}}<span class="sr-only">{{#str}}selected,local_trailscatalog{{/str}}</span>{{/view.cards}}
                        </a>
                        <a href="?categoryid={{search.categoryid}}&view=list" class="btn-view btn-list-view {{#view.list}}active{{/view.list}} btn btn-dark">
                            <i class="fa fa-list pr-1" aria-hidden="true"></i> {{#str}}list,local_trailscatalog{{/str}}
                            {{#view.list}}<span class="sr-only">{{#str}}selected,local_trailscatalog{{/str}}</span>{{/view.list}}
                        </a>
                    </div>
                </div>
                {{/has_categories}}
                {{#backbutton}}
                <div class="navitem justify-content-end">
                    <a class="btn btn-primary" href="{{backbutton}}">{{#str}}back, core{{/str}}</a>
                </div>
                {{/backbutton}}
            </div>
        </div>
    {{/filters}}

    <div>
        {{$content}} {{/content}}
    </div>
</div>

{{#is_admin}}
    {{#js}}
    require(['jquery', 'jqueryui', 'core_form/modalform', 'core/notification'], function($, ui, ModalForm, Notification) {
        let currentURL = window.location.href;

        $(document).on("click", ".btn-edit-category", function(e){
            e.preventDefault();
            let $button = $(this);
            let parentid = $button.data("parent") ? $button.data("parent") : "0";

            const modalForm = new ModalForm({
                formClass: "local_trailscatalog\\form\\category_form",
                args: {category: $button.data("id"), parent: parentid},
                modalConfig: {title: $button.data("title")},
                //returnFocus: $button[0],
            });

            let action = $button.data("id") ? "edit_category" : "save_category";
            let redirectTo = currentURL;

            modalForm.addEventListener(modalForm.events.LOADED, (e) => formLoaded(modalForm, "category"));
            modalForm.addEventListener(modalForm.events.SERVER_VALIDATION_ERROR, (e) => formLoaded(modalForm, "category"));
            modalForm.addEventListener(modalForm.events.FORM_SUBMITTED, (e) => window.location.href = redirectTo);
            modalForm.show();
        })

        function formLoaded(modalForm, form){
            check_modal_loaded(modalForm, form);
        }

        function check_modal_loaded(modalForm, form){
            let $modalBody = modalForm.modal.body;

            if(!$modalBody.find("form").length){
                setTimeout(function(){
                    check_modal_loaded(modalForm, form);
                },100);
            }else{
                setTimeout(function(){
                    format_form(modalForm, form);
                }, 100);
                return;
            }
        }

        function format_form(modalForm, formtype){
            let $formattedForm = $(".formatted-"+formtype+"-form").clone();
            let $modalBody = modalForm.modal.body;

            modalForm.modal.modal.addClass("modal-"+formtype+"-form");

            $modalBody.find(".fitem").each(function(){
                let inputName = $(this).find("input, select").attr("name");
                $(this).find(".col-md-3, .col-md-9").removeClass("col-md-3 col-md-9");

                if($(this).is("[data-groupname]")){
                    inputName = $(this).data("groupname");
                }

                $(this).appendTo($formattedForm.find(".field-"+inputName+"-container"));
            });

            if(!modalForm.modal.footer.find(".fdescription.required").length){
                $modalBody.find(".fdescription.required").addClass("mr-auto").prependTo(modalForm.modal.footer);
            }else{
                $modalBody.find(".fdescription.required").remove();
            }

            $formattedForm.removeClass("d-none");
            $formattedForm.prependTo($modalBody.find("form"));
        }
    });
    {{/js}}
{{/is_admin}}
