<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * @package   theme_smart
 * @copyright 2016 <PERSON>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($ADMIN->fulltree) {
    $settings = new theme_smart_admin_settingspage_tabs('themesettingsmart', get_string('configtitle', 'theme_smart'));
    $page = new admin_settingpage('theme_smart_general', get_string('generalsettings', 'theme_smart'));

    // Unaddable blocks.
    // Blocks to be excluded when this theme is enabled in the "Add a block" list: Administration, Navigation, Courses and
    // Section links.
    $default = 'navigation,settings,course_list,section_links';
    $setting = new admin_setting_configtext('theme_smart/unaddableblocks',
        get_string('unaddableblocks', 'theme_smart'), get_string('unaddableblocks_desc', 'theme_smart'), $default, PARAM_TEXT);
    $page->add($setting);

    // Preset.
    $name = 'theme_smart/preset';
    $title = get_string('preset', 'theme_smart');
    $description = get_string('preset_desc', 'theme_smart');
    $default = 'default.scss';

    $context = context_system::instance();
    $fs = get_file_storage();
    $files = $fs->get_area_files($context->id, 'theme_smart', 'preset', 0, 'itemid, filepath, filename', false);

    $choices = [];
    foreach ($files as $file) {
        $choices[$file->get_filename()] = $file->get_filename();
    }
    // These are the built in presets.
    $choices['default.scss'] = 'default.scss';
    $choices['plain.scss'] = 'plain.scss';

    $setting = new admin_setting_configthemepreset($name, $title, $description, $default, $choices, 'smart');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);
    
    // Checkbox para habilitar ou desabilitar o footer para administrador
    $setting = new admin_setting_configcheckbox(
        'theme_smart/footerHiden',
        get_string('footer_hiden_user', 'theme_smart'),
        get_string('footer_hiden_user_desc', 'theme_smart'),
        $default, PARAM_TEXT);
    $page->add($setting);

    // Checkbox para habilitar ou desabilitar o footer para usuários
    $setting = new admin_setting_configcheckbox(
        'theme_smart/enable_mustaches_footer',
        get_string('enable_mustaches_footer', 'theme_smart'),
        get_string('enable_mustaches_footer_desc', 'theme_smart'),
        $default, PARAM_TEXT);
    $page->add($setting);

    // Checkbox para habilitar ou desabilitar o footer para a area de login
    $setting = new admin_setting_configcheckbox(
        'theme_smart/enable_login_footer',
        get_string('enable_login_footer', 'theme_smart'),
        get_string('enable_login_footer_desc', 'theme_smart'),
        $default, PARAM_TEXT);
    $page->add($setting);

    // Preset files setting.
    $name = 'theme_smart/presetfiles';
    $title = get_string('presetfiles','theme_smart');
    $description = get_string('presetfiles_desc', 'theme_smart');

    $setting = new admin_setting_configstoredfile($name, $title, $description, 'preset', 0,
        array('maxfiles' => 20, 'accepted_types' => array('.scss')));
    $page->add($setting);

    // Background image setting.
    $name = 'theme_smart/backgroundimage';
    $title = get_string('backgroundimage', 'theme_smart');
    $description = get_string('backgroundimage_desc', 'theme_smart');
    $setting = new admin_setting_configstoredfile($name, $title, $description, 'backgroundimage');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Login Background image setting.
    $name = 'theme_smart/loginbackgroundimage';
    $title = get_string('loginbackgroundimage', 'theme_smart');
    $description = get_string('loginbackgroundimage_desc', 'theme_smart');
    $setting = new admin_setting_configstoredfile($name, $title, $description, 'loginbackgroundimage');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);
    
    // Login Logo
    $name = 'theme_smart/loginlogo';
    $title = get_string('loginlogo', 'theme_smart');
    $description = get_string('loginlogo_desc', 'theme_smart');
    $setting = new admin_setting_configstoredfile($name, $title, $description, 'loginlogo');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Footer logo
    $name = 'theme_smart/footerlogo';
    $title = get_string('footerlogo', 'theme_smart');
    $description = get_string('footerlogo_desc', 'theme_smart');
    $setting = new admin_setting_configstoredfile($name, $title, $description, 'footerlogo');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Variable $body-color.
    // We use an empty default value because the default colour should come from the preset.
    $name = 'theme_smart/brandcolor';
    $title = get_string('brandcolor', 'theme_smart');
    $description = get_string('brandcolor_desc', 'theme_smart');
    $setting = new admin_setting_configcolourpicker($name, $title, $description, '');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Cor primaria dos btns
    $name = 'theme_smart/btn_primary_bg_color';
    $title = get_string('btn_primary_bg_color', 'theme_smart');
    $description = get_string('btn_primary_bg_color_desc', 'theme_smart');
    $setting = new admin_setting_configcolourpicker($name, $title, $description, '');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Cor primaria dos textos dos btns
    $name = 'theme_smart/btn_primary_text_color';
    $title = get_string('btn_primary_text_color', 'theme_smart');
    $description = get_string('btn_primary_text_color_desc', 'theme_smart');
    $setting = new admin_setting_configcolourpicker($name, $title, $description, '');
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);
    
    // Must add the page after definiting all the settings!
    $settings->add($page);

    // Advanced settings.
    $page = new admin_settingpage('theme_smart_advanced', get_string('advancedsettings', 'theme_smart'));

    // Raw SCSS to include before the content.
    $setting = new admin_setting_scsscode('theme_smart/scsspre',
        get_string('rawscsspre', 'theme_smart'), get_string('rawscsspre_desc', 'theme_smart'), '', PARAM_RAW);
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    // Raw SCSS to include after the content.
    $setting = new admin_setting_scsscode('theme_smart/scss', get_string('rawscss', 'theme_smart'),
        get_string('rawscss_desc', 'theme_smart'), '', PARAM_RAW);
    $setting->set_updatedcallback('theme_reset_all_caches');
    $page->add($setting);

    $settings->add($page);
	
	

    // course settings.
    $modlist = [];
    
    foreach(core_component::get_plugin_list('mod') as $mod => $v){
        $modlist[$mod] = get_string("pluginname", $mod);
    }

    asort($modlist);

    $page = new admin_settingpage('theme_smart_course', get_string('coursesettings', 'theme_smart'));
    $setting = new admin_setting_configmultiselect(
        'theme_smart/navmods', 
        get_string('activitynavigationmods', 'theme_smart'), 
        get_string('activitynavigationmods_desc', 'theme_smart'), 
        [], 
        $modlist
    );
    $page->add($setting);
    $settings->add($page);
   


    // Social settings.
    $page = new admin_settingpage('theme_smart_social', get_string('socialsettings', 'theme_smart'));
    
	$setting = new admin_setting_configtextarea('theme_smart/companyaddress', get_string('companyaddress', 'theme_smart'), "", '', PARAM_RAW);
    $page->add($setting);
	
	// Social Title
	$setting = new admin_setting_heading('social_title', get_string('social_title', 'theme_smart'), "");
    $page->add($setting);
	
	// Facebook
    $setting = new admin_setting_configtext('theme_smart/social_facebook', get_string('social_facebook', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Twitter
    $setting = new admin_setting_configtext('theme_smart/social_twitter', get_string('social_twitter', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Instagram
    $setting = new admin_setting_configtext('theme_smart/social_instagram', get_string('social_instagram', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Linkedin
    $setting = new admin_setting_configtext('theme_smart/social_linkedin', get_string('social_linkedin', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Youtube
    $setting = new admin_setting_configtext('theme_smart/social_youtube', get_string('social_youtube', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Whatsapp
    $setting = new admin_setting_configtext('theme_smart/social_whatsapp', get_string('social_whatsapp', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Apps Title
	$setting = new admin_setting_heading('apps_title', get_string('apps_title', 'theme_smart'), "");
    $page->add($setting);
	
	// Apple Store
    $setting = new admin_setting_configtext('theme_smart/app_applestore', get_string('app_applestore', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);
	
	// Google Play
    $setting = new admin_setting_configtext('theme_smart/app_googleplay', get_string('app_googleplay', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);

    $settings->add($page);


    // Configurações de inteligencia artificial
    $page = new admin_settingpage('theme_smart_ai', get_string('aisettings', 'theme_smart'));

    $setting = new admin_setting_heading('ai_title', get_string('ai_title', 'theme_smart'), "");
    $page->add($setting);

    $setting = new admin_setting_configcheckbox(
        'theme_smart/enable_ai_menu',
        get_string('enable_ai_menu', 'theme_smart'),
        get_string('enable_ai_menu_desc', 'theme_smart'),
        $default,
        PARAM_TEXT
    );
    $page->add($setting);

    $setting = new admin_setting_configtext('theme_smart/ai_assistantid', get_string('ai_assistantid', 'theme_smart'), "", "", PARAM_TEXT);
    $page->add($setting);

    $settings->add($page);

    // Configurações de HTML personalizado
    $page = new admin_settingpage('theme_smart_custom_html', get_string('customhtmlsettings', 'theme_smart'));

    $setting = new admin_setting_heading('custom_html_title', get_string('custom_html_title', 'theme_smart'), "");
    $page->add($setting);

    // Campo para HTML personalizado
    $setting = new admin_setting_configtextarea(
        'theme_smart/custom_html',
        get_string('custom_html', 'theme_smart'),
        get_string('custom_html_desc', 'theme_smart'),
        '',
        PARAM_RAW
    );
    $page->add($setting);

    // Checkbox para habilitar apenas na área logada
    $setting = new admin_setting_configcheckbox(
        'theme_smart/custom_html_logged_only',
        get_string('custom_html_logged_only', 'theme_smart'),
        get_string('custom_html_logged_only_desc', 'theme_smart'),
        1,
        PARAM_INT
    );
    $page->add($setting);

    $settings->add($page);


}
