<?php

defined('MOODLE_INTERNAL') || die();

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_certificates($userid)
{
    $simplecertificates = local_certificatepage_get_user_simple_certificates($userid);
    $customcertificates = local_certificatepage_get_user_custom_certificates($userid);

    $withlinkedinmodule = get_config('local_certificatepage', 'viewlinkedincertificates');

    if($withlinkedinmodule && has_mod_linkedin()) {
        $linkedincertificates = local_certificatepage_get_user_linkedin_certificates($userid);

        $allusercertificates = array_merge($simplecertificates, $customcertificates, $linkedincertificates);

    } else {
        $allusercertificates = array_merge($simplecertificates, $customcertificates);
    }

    uasort($allusercertificates, function($certificate, $nextcertificate) {
        return $nextcertificate->timecreated > $certificate->timecreated;
    });

    return $allusercertificates;
}

/**
 * Check if mod_linkedin is installed.
 * @return bool
 */
function has_mod_linkedin()
{
    global $DB;

    return $haslinkedinmod = $DB->record_exists(
        $table = 'modules',
        $conditions = [
            'name' => 'linkedin',
            'visible' => 1
        ]
    );
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_simple_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql = <<<SQL

SELECT  issues.*,
        null cover,
        cm.id module,
        cm.course course
FROM {simplecertificate} cert

  JOIN {simplecertificate_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN mdl_modules m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
-- AND cm.visible = :visible
SQL;

    $param = [
        'userid'  => $userid,
        'name'    => 'simplecertificate',
        'visible' => 1
    ];

    $placeholder = "pix/cinza-placeholder.jpg";

    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates = $DB->get_records_sql($sql, $param)) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'simplecertificate';
        }
    }

    return $certificates;
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_custom_certificates($userid)
{
    global $DB, $OUTPUT;

    // Verificar se o plugin customcert está instalado
    if (!$DB->record_exists('modules', ['name' => 'customcert', 'visible' => 1])) {
        return [];
    }

    $sql = <<<SQL

SELECT  issues.*,
        issues.customcertid as certificateid,
        cert.name as certificatename,
        c.fullname as coursename,
        null cover,
        cm.id module,
        cm.course course
FROM {customcert} cert

  JOIN {customcert_issues} issues
    ON cert.id = issues.customcertid
  JOIN {course} c
    ON c.id = cert.course
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
-- AND cm.visible = :visible
SQL;

    $param = [
        'userid'  => $userid,
        'name'    => 'customcert',
        'visible' => 1
    ];

    $placeholder = "pix/cinza-placeholder.jpg";

    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates = $DB->get_records_sql($sql, $param)) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'customcert';
        }
    }

    return $certificates ?: [];
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_linkedin_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql = <<<SQL

SELECT  issues.*,
        null cover,
        cm.id module,
        cm.course course
FROM {linkedin} cert

  JOIN {linkedin_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN mdl_modules m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
-- AND cm.visible = :visible
SQL;

    $param = [
        'userid'  => $userid,
        'name'    => 'linkedin',
        'visible' => 1
    ];

    $placeholder = "pix/cinza-placeholder.jpg";

    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates = $DB->get_records_sql($sql, $param)) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'linkedin';
        }
    }

    return $certificates;
}

/**
 * @param $course
 * @return null|string
 */
function local_certificatepage_get_course_image($course){
    global $CFG;

    if (!$course instanceof core_course_list_element) {
        $course = new \core_course_list_element($course);
    }

    $courseTrait = new class{
        use \local_courseblockapi\traits\course_trait;

        public function getCourseImage($course, $type) {
            return $this->get_course_image($course, "object");
        }
    };

    $courseimages = $courseTrait->getCourseImage($course, "object");

    if($courseimages && $courseimages->card){
        return $courseimages->card;
    }

    return null;
}

function local_certificatepage_get_default_course_image() {
    $fs = get_file_storage();

    // Returns an array of `stored_file` instances.
    $files = $fs->get_area_files(1, 'local_certificatepage', 'defaultcourseimage');

    foreach ($files as $file) {
        if($file->get_filename() === '.') continue;

        $url = moodle_url::make_pluginfile_url(
            $file->get_contextid(),
            $file->get_component(),
            $file->get_filearea(),
            $file->get_itemid(),
            $file->get_filepath(),
            $file->get_filename(),
            false                     // Do not force download of the file.
        );

        return $url;
    }

    return null;
}