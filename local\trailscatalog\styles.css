.trailscatalog-page .trailscatalog-header {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.trailscatalog-page .trailscatalog-header .actions {
  display: flex;
  gap: 10px;
}

.trailscatalog-page .trailscatalog-header .actions .back-button {
  display: flex;
  align-items: center;
  height: 34px;
}

#category-list,
#course-list > .card-courses {
  gap: 25px 0;
}

#category-list .card {
  cursor: pointer;
  border: 0;
  overflow: hidden;
  height: var(--card-height);
}

#category-list .card .card-img {
  width: 100%;
  height: var(--img-height);
  max-height: 350px;
  object-fit: cover;
}

#category-list .card .card-img-overlay {
  /* opacity: 0.8;
  background: radial-gradient(
    circle,
    rgb(0 0 0 / 5%) 0%,
    rgb(0 0 0 / 75%) 100%
  ); */

  -webkit-transition: opacity 0.8s ease-in-out;
  -moz-transition: opacity 0.8s ease-in-out;
  -o-transition: opacity 0.8s ease-in-out;
  transition: opacity 0.8s ease-in-out;
}

#category-list .card .card-img-overlay:hover {
  zoom: 1;
  filter: alpha(opacity=100);
  opacity: 1;

  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.2) 0%,
    rgb(0 0 0 / 20%) 100%
  );
}

#category-list .card .card-img-overlay .card-title {
  position: absolute;
  width: 80%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  text-align: center;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .trailscatalog-page .trailscatalog-header {
    gap: 20px;
    flex-direction: column-reverse;
  }

  .trailscatalog-page #action_bar .navitem {
    width: 100%;
    margin-right: 0;
  }

  .trailscatalog-page #action_bar .navitem form .custom-select {
    width: 100%;
  }

  .trailscatalog-page #action_bar .navitem .simplesearchform form {
    padding-right: 0;
  }
}

.path-local-trailscatalog .dimmed,
.path-local-trailscatalog .card-img-top:not(.visible),
.path-local-trailscatalog .card-body:not(.visible){
  opacity: 0.5;
}

.path-local-trailscatalog .modal-dialog .row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.path-local-trailscatalog .modal-dialog label {
  margin-right: 0;
}

.path-local-trailscatalog .field-parent-container .felement {
  display: flex;
  flex-direction: column-reverse;
}

.path-local-trailscatalog .modal-dialog .form-inline,
.path-local-trailscatalog .modal-dialog input[type='text'],
.path-local-trailscatalog .modal-dialog select {
  width: 100% !important;
}

.path-local-trailscatalog .form-filetypes-descriptions ul {
  display: flex;
}

.path-local-trailscatalog .form-filetypes-descriptions ul li:not(:last-child):after{
  content: ", ";
}

.path-local-trailscatalog .form-filetypes-descriptions ul li{
  padding-right: 0.5rem;
}

.path-local-trailscatalog .btn-view.active{
  pointer-events: none;
}

.path-local-trailscatalog .card:focus-within,
.path-local-trailscatalog .list-item:focus-within{
	box-shadow: 0 0 0 .2rem var(--primary) !important;
}


#page-local-trailscatalog-index.path-local-trailscatalog .page-item {
  margin: 0;
}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item-previous {
    margin-right: 6px;
}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item-next {
    margin-left: 6px;
}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item a.page-link {

}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item.active a.page-link {
  background-color: #007bff !important;
  color: #fff !important;
  border-color: #007bff;
}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item a.page-link:focus {
  outline: none;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

#page-local-trailscatalog-index.path-local-trailscatalog .page-item.disabled a.page-link {
  opacity: 0.5;
  pointer-events: none;
}