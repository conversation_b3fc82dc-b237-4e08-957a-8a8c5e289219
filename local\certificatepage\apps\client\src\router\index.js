import * as Config from 'core/config'
import { createRouter, createWebHistory, createMemoryHistory } from 'vue-router'

const basePath = '/local/certificatepage/'

const createCustomRouter = (isBlock) => {
  const dynamicPath = (() => {
    const host = window.location.host
    const currentPath = window.location.pathname
    const relativePath = Config.wwwroot
      .replace(/^https?\:\/\//i, '')
      .replace(host, '')
      .concat(basePath)
    return currentPath.includes('index.php') ? relativePath + 'index.php' : relativePath
  })()

  const routes = [
    {
      path: '/',
      name: 'my.certificates.index',
      component: () => import('../views/MyCertificatesView/Index.vue')
    },
    {
      path: '/trails_certificates',
      name: 'trails.certificates.index',
      component: () => import('../views/TrailsCertificatesView/Index.vue')
    },
  ]

  const historyType = isBlock ? createMemoryHistory : createWebHistory

  return createRouter({
    history: historyType(dynamicPath),
    routes
  })
}

export default createCustomRouter
