{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_smart/drawers

    Boost drawer template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * courseindexopen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON>od<PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "courseindexopen": true,
        "navdraweropen": false,
        "blockdraweropen": true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false,
        "addblockbutton": ""
    }
}}
{{> theme_smart/head }}

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}

{{{ output.standard_top_of_body_html }}}

<div id="page-wrapper" class="d-print-block">

    {{#courseindex}}
        {{< theme_smart/drawer }}
            {{$id}}theme_smart-drawers-courseindex{{/id}}
            {{$drawerclasses}}drawer drawer-left border-right shadow {{#courseindexopen}}show{{/courseindexopen}}{{/drawerclasses}}
            {{$drawercontent}}
                {{{courseindex}}}
            {{/drawercontent}}
            {{$drawerpreferencename}}drawer-open-index{{/drawerpreferencename}}
            {{$drawerstate}}show-drawer-left{{/drawerstate}}
            {{$tooltipplacement}}right{{/tooltipplacement}}
            {{$closebuttontext}}{{#str}}closecourseindex, core{{/str}}{{/closebuttontext}}
			{{$drawertitle}} {{drawertitle}} {{/drawertitle}}
        {{/ theme_smart/drawer}}
    {{/courseindex}}
	
		{{#hasblocks}}
        {{< theme_smart/drawer }}
            {{$id}}theme_smart-drawers-blocks{{/id}}
            {{$drawerclasses}}drawer drawer-right moodle-has-zindex border-left shadow{{#blockdraweropen}} show{{/blockdraweropen}}{{/drawerclasses}}
            {{$drawercontent}}
                <section class="d-print-none px-3" aria-label="{{#str}}blocks{{/str}}">
									{{{ addblockbutton }}}
									{{{ sidepreblocks }}}
                </section>
            {{/drawercontent}}
            {{$drawerpreferencename}}drawer-open-block{{/drawerpreferencename}}
            {{$forceopen}}{{#forceblockdraweropen}}1{{/forceblockdraweropen}}{{/forceopen}}
            {{$drawerstate}}show-drawer-right{{/drawerstate}}
            {{$tooltipplacement}}left{{/tooltipplacement}}
            {{$drawercloseonresize}}1{{/drawercloseonresize}}
            {{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
        {{/ theme_smart/drawer}}
    {{/hasblocks}}
	
    {{^hasblocks}}
			<div 
			   class="drawer drawer-right border-left moodle-has-zindex is-assistant shadow {{#blockdraweropen}}show{{/blockdraweropen}} d-print-none not-initialized"
			   data-region="fixed-drawer"
			   id="theme_smart-drawers-blocks"
			   data-preference="drawer-open-block"
			   data-state="show-drawer-right"
			   data-forceopen="{{#forceblockdraweropen}}1{{/forceblockdraweropen}}"
			   data-close-on-resize="1"
			>
				<div class="drawerheader d-flex align-items-center border-0 justify-content-between py-1 px-1 w-100 border-bottom">
					<button
						class="btn btn-light rounded-circle shadow drawertoggle togglejudy icon-no-margin hidden"
						style="right:0.5rem; top:1rem; z-index:10; padding:0.5rem" 
						data-toggler="drawers"
						data-action="closedrawer"
						data-target="theme_smart-drawers-blocks"
						data-toggle="tooltip"
						data-placement="{{$tooltipplacement}}left{{/tooltipplacement}}"
						title="{{$closebuttontext}}{{#str}}closedrawer, core{{/str}}{{/closebuttontext}}"
					>
						{{#pix}} e/cancel, core {{/pix}}
					</button>
				</div>

				<div class="drawercontent drag-container assistant-container p-0 vh-100" data-usertour="scroller">
					<section class="d-print-none">
						{{{ addblockbutton }}}
            {{{ sidepreblocks }}}
					</section>
				</div>
			</div>
    {{/hasblocks}}
	
	<div class="site-wrapper">
		{{^hasformatsite}}
		<div id="page" data-region="mainpage" data-usertour="scroller" class="drawers {{#courseindexopen}}show-drawer-left{{/courseindexopen}} {{#blockdraweropen}}show-drawer-right{{/blockdraweropen}} drag-container px-0">
			<div class="mini-logo position-absolute mt-3 mx-3 d-none d-lg-block" style="z-index:20; left:0">
				{{>theme_smart/logo}}
			</div>
				
			{{{ output.full_header }}}
		
			<div class="secondary-navigation d-flex justify-content-end align-items-center d-print-none mt-md-4 mt-3 mr-3 mr-md-0">
				<div class="{{^output.smart_edit_button}}mr-2{{/output.smart_edit_button}}">
					<a href="{{home_url}}" class="btn btn-dark rounded-circle" title="{{#str}}exitcourse, theme_smart{{/str}}" data-toggle="tooltip" data-placement="bottom" aria-label="{{#str}}exitcourse, theme_smart{{/str}}">
						<i class="fa fa-arrow-right-from-bracket pl-1"></i>
					</a>
				</div>
				
				{{#output.course_headeractions}}
					<div class="d-flex align-items-center">
						<div class="header-actions-container" data-region="header-actions-container">
							{{#headeractions}}
								<div class="header-action">{{{.}}}</div>
							{{/headeractions}}
						</div>
					</div>
				{{/output.course_headeractions}}
			
				{{#output.smart_edit_button}}
					<div class="ml-3" title="{{editstring}}" data-toggle="tooltip" data-placement="bottom">
						{{{ output.edit_switch }}}
					</div>
				{{/output.smart_edit_button}}
				
				{{#secondarymoremenu}}
					<div class="ml-1">
						{{>theme_smart/course_menu}}
					</div>
				{{/secondarymoremenu}}
			</div>
			
			<div id="topofscroll" class="main-inner mt-0">
				<div class="drawer-toggles {{^courseindex}}{{^hasrightdrawer}}d-none{{/hasrightdrawer}}{{/courseindex}}">
					{{#courseindex}}
						<div class="drawer-toggler drawer-left-toggle open-nav d-print-none">
							<button
								class="btn btn-light icon-no-margin py-1 pr-2"
								data-toggler="drawers"
								data-action="toggle"
								data-target="theme_smart-drawers-courseindex"
								data-toggle="tooltip"
								data-placement="right"
								title="{{#str}}opendrawerindex, core{{/str}}"
							>
								<span class="sr-only">{{#str}}opendrawerindex, core{{/str}}</span>
								{{#pix}} t/index_drawer, moodle {{/pix}}
								<span class="d-block d-md-none label">{{drawertitle}}</span>
							</button>
						</div>
					{{/courseindex}}
					
					{{#hasrightdrawer}}
						<div class="drawer-toggler drawer-right-toggle ml-auto d-print-none">
							<button
								class="btn btn-light icon-no-margin togglejudy py-1 pl-2"
								data-toggler="drawers"
								data-action="toggle"
								data-target="theme_smart-drawers-blocks"
							>
								<i class="icon icon-assistantgpt"></i>
								<span class="d-block d-md-none label">Ver blocos/span>
							</button>
						</div>
					{{/hasrightdrawer}}
				</div>
				
				<div id="page-content" class="pb-3 px-2 px-md-0 d-print-block">
					<div id="region-main-box">
						{{#hasregionmainsettingsmenu}}
						<div id="region-main-settings-menu" class="d-print-none">
							<div> {{{ regionmainsettingsmenu }}} </div>
						</div>
						{{/hasregionmainsettingsmenu}}
						<section id="region-main" aria-label="{{#str}}content{{/str}}">

							{{#hasregionmainsettingsmenu}}
								<div class="region_main_settings_menu_proxy"></div>
							{{/hasregionmainsettingsmenu}}
							{{{ output.course_content_header }}}
							{{#headercontent}}
								{{> core/activity_header }}
							{{/headercontent}}
							{{#overflow}}
								<div class="container-fluid tertiary-navigation">
									<div class="navitem">
										{{> core/url_select}}
									</div>
								</div>
							{{/overflow}}
							{{{ output.main_content }}}
							{{{ output.activity_navigation }}}
							{{{ output.course_content_footer }}}

						</section>
					</div>
				</div>
			</div>
			{{> theme_smart/footer }}
		</div>
		{{/hasformatsite}}
		{{#hasformatsite}}
			{{> theme_smart/xp }}
		{{/hasformatsite}}
		{{{ output.standard_after_main_region_html }}}
	</div>
</div>

{{{ output.smart_custom_html }}}

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader', 'theme_smart/drawer', 'theme_smart/drawers'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_smart/loader');
});
{{/js}}