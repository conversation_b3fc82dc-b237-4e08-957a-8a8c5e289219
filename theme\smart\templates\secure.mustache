{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_smart/secure

    Smart secure layout template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page

      Example context (json):
    {
        "sitename": "Moodle",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         }
    }
}}
{{> theme_smart/head }}

<body {{{ bodyattributes }}} data-theme='dark'>
{{> core/local/toast/wrapper}}

<div id="page-wrapper">

    {{{ output.standard_top_of_body_html }}}

    {{>theme_smart/navbar-secure}}

    <div id="page" class="container-fluid">
        {{! Secured full header }}

        <div id="page-header" class="row">
            <div class="col-12 py-3">
                <div class="page-context-header">
                    <div class="page-header-headings">
                        {{{ output.page_heading }}}
                    </div>
                </div>
            </div>
        </div>

        <div id="page-content" class="row">
            <div id="region-main-box" class="col-12">
                <section id="region-main" {{#hasblocks}}class="has-blocks"{{/hasblocks}} aria-label="{{#str}}content{{/str}}">

                    {{{ output.course_content_header }}}
                    {{{ output.main_content }}}
                    {{{ output.course_content_footer }}}

                </section>
                {{#hasblocks}}
                <section data-region="blocks-column" aria-label="{{#str}}blocks{{/str}}">
                    {{{ sidepreblocks }}}
                </section>
                {{/hasblocks}}
            </div>
        </div>
    </div>
    <footer id="page-footer" class="py-3">
        <div class="container">
            <div id="course-footer">{{{ output.course_footer }}}</div>
            {{{ output.standard_end_of_body_html }}}
        </div>
    </footer>
</div>

{{{ output.smart_custom_html }}}

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader'], function() {
    M.util.js_complete('theme_smart/loader');
});
{{/js}}
