<?php

namespace local_trailscatalog\output;

use core_course_category;
use course_request;
use html_writer;
use moodle_url;
use renderable;
use templatable;
use renderer_base;
use stdClass;

require_once($CFG->dirroot . '/local/trailscatalog/locallib.php');
require_once($CFG->dirroot . '/course/lib.php');

abstract class page implements renderable, templatable
{
    /** @var context $context */
    protected $context;

    /** @var stdClass $category */
    protected $category;

    /** @var string $search */
    protected $search;

    /** @var int $page */
    protected $page;

    /**
     * Constructs a new instance of the class.
     *
     * @param int $page The page number
     * @param int $categoryid The ID of the category (default: 0)
     * @param string $search The search query (default: '')
     * @return void
     */
    public function __construct($page, $categoryid = 0, $search = '')
    {
        $category = core_course_category::get($categoryid);

        $this->page = $page;
        $this->category = $category;
        $this->search = $search;

        $this->context = \context_system::instance();
    }

    /**
     * Export the data for template.
     *
     * @param renderer_base $output The renderer object.
     * @return array The data to be used in the template.
     */
    public function export_for_template(renderer_base $output): array
    {
        global $PAGE;

        $categories = core_course_category::make_categories_list();

        $url = new moodle_url($PAGE->url, ['categoryid' => 0]);
        $options =  [
            [
                'value' => $url->out(),
                'name' =>  get_string('all')
            ]
        ];

        $currenturl = new moodle_url($PAGE->url, ['categoryid' => $this->category->id ?? 0]);

        foreach ($categories as $id => $cat) {
            $url = new moodle_url($PAGE->url, ['categoryid' => $id]);
            $options[] = [
                'value' => $url->out(),
                'name' => $cat,
                'selected' => $url->out() === $currenturl->out()
            ];
        }

        $view_type = get_user_preferences('local_trailscatalog_view', 'cards');

        $data = [
            'page' => [
                'title' => $this->get_title(),
                'description' => $this->get_description(),
                'returnurl' => $this->get_return_url(),
                'additionaloptions' => $this->get_additional_category_options(),
            ],
            'filters' => [
                'categoryselect' => [
                    'formid' => html_writer::random_id('url_select_form_'),
                    'action' => (new moodle_url($PAGE->url))->out(false),
                    'id' => html_writer::random_id('url_select_'),
                    'options' => $options,
                    'sesskey' => sesskey()
                ],
                'search' => [
                    'action' => new moodle_url('/local/trailscatalog/index.php'),
                    'query' => $this->search,
                    'categoryid' => $this->category->id ?? 0
                ],
            ],
            'view' => [
                'cards' => $view_type == "cards" || false,
                'list' => $view_type == "list" || false,
            ],
            'has_categories' => !empty($categories)
        ];

        return $data;
    }

    /**
     * Retrieves the title based on the search query and the category.
     *
     * @return string The title of the page.
     */
    protected function get_title(): string
    {
        if (!empty($this->search)) {
            return get_string('pluginname', 'local_trailscatalog');
        } elseif ($this->category->id) {
            // Garantir que o nome da categoria nunca seja nulo
            return !empty($this->category->name) ? $this->category->name : '';
        } else {
            return get_string('pluginname', 'local_trailscatalog');
        }
    }

    /**
     * Retrieves the description based on the search query and the category.
     *
     * @return string The description of the page.
     */
    protected function get_description(): string
    {
        if (!empty($this->search)) {
            return get_string('search_results', 'local_trailscatalog');
        } elseif ($this->category->id) {
            // Garantir que a descrição da categoria nunca seja nula
            return !empty($this->category->description) ? $this->category->description : '';
        } else {
            return get_string('all_categories', 'local_trailscatalog');
        }
    }

    /**
     * Retrieves the return URL based on the current category and search query.
     *
     * @return moodle_url|false The return URL, or false if the top category is the current category and the search query is empty.
     */
    protected function get_return_url()
    {
        global $PAGE;

        $topcategoryid = core_course_category::top()->id;

        if ($topcategoryid === ($this->category->id ?? 0) && empty($this->search)) {
            return false;
        }

        $urlparams = ['categoryid' => $this->search ? ($this->category->id ?? 0) : ($this->category->parent ?? 0)];

        return new moodle_url($PAGE->url, $urlparams);
    }

    /**
     * Retrieves additional category options based on the current category and user capabilities.
     *
     * @return array An array containing the additional category options, with each option being an associative array with 'url' and 'string' keys.
     */
    protected function get_additional_category_options(): array
    {
        global $CFG, $DB, $USER;

        $is_client = theme_smart_is_client($USER->id);

        if ($is_client) {
            $options[] = [
                'url' => new moodle_url('/admin/tool/coursemanagement/index.php'),
                'string' => get_string('managecourses', 'local_trailscatalog')
            ];

            return ['options' => $options];
        }

        if ($this->category->is_uservisible()) {
            $context = get_category_or_system_context($this->category->id);
            if (has_capability('moodle/course:create', $context)) {
                $params = [
                    'category' => $this->category->id ?: $CFG->defaultrequestcategory,
                    'returnto' => $this->category->id ? 'category' : 'topcat'
                ];

                $options[0] = [
                    'url' => new moodle_url('/course/edit.php', $params),
                    'string' => get_string('addnewcourse', 'local_trailscatalog')
                ];
            }

            if (!empty($CFG->enablecourserequests)) {
                // Display an option to request a new course.
                if (course_request::can_request($context)) {
                    $params = [];
                    if ($context instanceof context_coursecat) {
                        $params['category'] = $context->instanceid;
                    }

                    $options[3] = [
                        'url' => new moodle_url('/course/request.php', $params),
                        'string' => get_string('requestcourse', 'core_course')
                    ];
                }

                // Display the manage pending requests option.
                if (has_capability('moodle/site:approvecourse', $context)) {
                    $disabled = !$DB->record_exists('course_request', array());
                    if (!$disabled) {
                        $options[4] = [
                            'url' => new moodle_url('/course/pending.php'),
                            'string' => get_string('coursespending', 'core_course')
                        ];
                    }
                }
            }
        }

        if ($this->category->can_create_course() || $this->category->has_manage_capability()) {
            // Add 'Manage' button if user has permissions to edit this category.
            $options[2] = [
                'url' => new moodle_url('/course/management.php', ['categoryid' => $this->category->id ?? 0]),
                'string' => get_string('managecourses', 'local_trailscatalog')
            ];

            if ($this->category->has_manage_capability()) {
                $addsubcaturl = new moodle_url('/course/editcategory.php', array('parent' => $this->category->id ?? 0));
                $options[1] = [
                    'url' => $addsubcaturl,
                    'string' => get_string('addsubcategory', 'local_trailscatalog')
                ];
            }
        }

        // We have stored the options in a predefined order. Sort it based on index and return.
        if (isset($options)) {
            sort($options);
            return ['options' => $options];
        }

        return [];
    }
}
