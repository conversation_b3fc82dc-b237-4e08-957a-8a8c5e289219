<?php
/**
 * @copyright 2024 Revvo
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @package local_trailscatalog
 */

namespace local_trailscatalog\form;

defined('MOODLE_INTERNAL') || die;

use \tool_lfxp\core\category\category_image;
use \core_course_category;

require_once($CFG->libdir.'/gdlib.php');
require_once($CFG->libdir.'/adminlib.php');
require_once($CFG->dirroot.'/webservice/lib.php');
require_once($CFG->dirroot.'/admin/tool/coursemanagement/lib.php');

class category_form extends \core_form\dynamic_form {

    /**
     * Define the form.
     */
    public function definition() {
        global $CFG, $DB;
        $mform = $this->_form;
        $categoryid = $this->custom_data()['categoryid'];
        $parent = $this->custom_data()['parent'];

        // Get list of categories to use as parents, with site as the first one.
        $options = array();
        if ($parent == 0) {
            $options[0] = get_string('top');
        }
        if ($categoryid) {
            // Editing an existing category.
            $options += core_course_category::make_categories_list('moodle/category:manage', $categoryid);
            if (empty($options[$parent])) {
                // Ensure the the category parent has been included in the options.
                $options[$parent] = $DB->get_field('course_categories', 'name', array('id'=>$parent));
            }
        } else {
            // Making a new category.
            $options += core_course_category::make_categories_list('moodle/category:manage');
        }

        //$mform->addElement('autocomplete', 'parent', get_string('parentcategory'), $options);
		$mform->addElement('select', 'parent', get_string('parentcategory'), $options);
        $mform->addRule('parent', null, 'required', null, 'client');

        $mform->addElement('text', 'name', get_string('categoryname'), array('size' => '30'));
        $mform->addRule('name', get_string('required'), 'required', null);
        $mform->setType('name', PARAM_TEXT);

        $mform->addElement('text', 'idnumber', get_string('idnumbercoursecategory'), 'maxlength="100" size="10"');
        $mform->addHelpButton('idnumber', 'idnumbercoursecategory');
        $mform->setType('idnumber', PARAM_RAW);

        $mform->addElement('editor', 'description_editor', get_string('description'), null,
            $this->get_description_editor_options());
        $mform->setType('description_editor', PARAM_RAW);

        if (!empty($CFG->allowcategorythemes)) {
            $themes = array(''=>get_string('forceno'));
            $allthemes = get_list_of_themes();
            foreach ($allthemes as $key => $theme) {
                if (empty($theme->hidefromselector)) {
                    $themes[$key] = get_string('pluginname', 'theme_'.$theme->name);
                }
            }
            $mform->addElement('select', 'theme', get_string('forcetheme'), $themes);
        }

       $mform->addElement(
            'filemanager',
            category_image::FIELDNAME,
            "Imagem da categoria",
            null,
            array(
                'maxfiles' => 1,
                'accepted_types' => array(
                    '.png',
                    '.jpg',
                    '.jpeg',
                    '.gif',
                )
            )
        );

        $mform->setDefault(category_image::FIELDNAME, $this->custom_data()['categoryimage']);

        $mform->addElement('hidden', 'id', 0);
        $mform->setType('id', PARAM_INT);
        $mform->setDefault('id', $categoryid);
    }

    public function custom_data() {
		$id = $this->optional_param('category', 0, PARAM_INT);

		if ($id) {
			$coursecat = core_course_category::get($id, MUST_EXIST, true);
			$category = $coursecat->get_db_record();
			$context = \context_coursecat::instance($id);
			$itemid = 0; // Initialise itemid, as all files in category description has item id 0.
		} else {
			$parent = optional_param('parent', 0, PARAM_INT);

			if($parent) {
				$parentcategory = $DB->get_record('course_categories', array('id' => $parent), '*', MUST_EXIST);
				$context = \context_coursecat::instance($parent);
			} else {
				$context = \context_system::instance();
			}

			$category = new \stdClass();
			$category->id = 0;
			$category->parent = $parent;
			$itemid = null; // Set this explicitly, so files for parent category should not get loaded in draft area.
		}

		//$draftitemid = file_get_submitted_draft_itemid('categoryimage');
		//file_prepare_draft_area($draftitemid, $context->id, 'tool_lfxp', 'categoryimage', $category->id, array('maxfiles' => 1));

		$draftitemid = category_image::prepare_draft_area($id, $context);

		return [
			'categoryid' => $id,
			'parent' => $category->parent,
			'context' => $context,
			'itemid' => $itemid,
			'categoryimage' => $draftitemid
		];
    }

    public function get_description_editor_options() {
        global $CFG;
        $context = $this->custom_data()['context'];
        $itemid = $this->custom_data()['itemid'];
        return array(
            'maxfiles'  => EDITOR_UNLIMITED_FILES,
            'maxbytes'  => $CFG->maxbytes,
            'trusttext' => false,
            'noclean'   => true,
            'context'   => $context,
            'subdirs'   => file_area_contains_subdirs($context, 'coursecat', 'description', $itemid),
        );
    }

    /**
     * Extend the form definition after data has been parsed.
     */
    public function definition_after_data() {
        global $CFG, $DB, $OUTPUT;

		return true;
    }

    /**
     * Validate the form data.
     * @param array $usernew
     * @param array $files
     * @return array|bool
     */
    public function validation($data, $files) {
        global $DB;
        $errors = parent::validation($data, $files);
        if (!empty($data['idnumber'])) {
            if ($existing = $DB->get_record('course_categories', array('idnumber' => $data['idnumber']))) {
                if (!$data['id'] || $existing->id != $data['id']) {
                    $errors['idnumber'] = get_string('categoryidnumbertaken', 'error');
                }
            }
        }
        return $errors;
    }

	/**
     * Returns context where this form is used
     *
     * @return context
     */
    protected function get_context_for_dynamic_submission(): \context {
        return \context_system::instance();
    }

    /**
     * Checks if current user has access to this form, otherwise throws exception
     */
    protected function check_access_for_dynamic_submission(): void {
        require_capability('tool/coursemanagement:edit', $this->get_context_for_dynamic_submission());
    }

    /**
     * Process the form submission, used if form was submitted via AJAX
     */
    public function process_dynamic_submission() {
        global $CFG, $DB;

		$data = $this->get_data();
		$coursecat = core_course_category::get($data->id, MUST_EXIST, true);

		if ($coursecat->id) {
			if((int)$data->parent !== (int)$coursecat->parent && !$coursecat->can_change_parent($data->parent)) {
				throw new \moodle_exception('cannotmovecategory');
			}
			$coursecat->update($data, $this->get_description_editor_options());
			$categoryid = $data->id;
		} else {
			$category = core_course_category::create($data, $this->get_description_editor_options());
			$categoryid = $category->id;
		}

		$context = \context_coursecat::instance($categoryid);

		$categoryimage = $data->{category_image::FIELDNAME};

		if(!empty($categoryimage)){
			category_image::save_draft_area_file($categoryimage, $categoryid, $context);
			//file_save_draft_area_files($data->categoryimage, $context->id, 'tool_lfxp', 'categoryimage', $categoryid, array('maxfiles' => 1));
		}

		return null;
    }

    /**
     * Load in existing data as form defaults
     */
    public function set_data_for_dynamic_submission(): void {
        global $DB;
        if ($id = $this->optional_param('category', 0, PARAM_INT)) {
            $category = $DB->get_record('course_categories', ['id' => $id], '*', MUST_EXIST);
			$category->description_editor = ['text' => $category->description, 'format' => FORMAT_HTML, 'itemid' => 0];

			$custom_data = (object)$this->custom_data();
			//$category->categoryimage = $custom_data->categoryimage;

			$this->set_data($category);
        }
    }

    /**
     * Page url
     * @return \moodle_url
     */
    protected function get_page_url_for_dynamic_submission(): \moodle_url {
        return new \moodle_url("/admin/tool/coursemanagement/index.php");
    }
}


