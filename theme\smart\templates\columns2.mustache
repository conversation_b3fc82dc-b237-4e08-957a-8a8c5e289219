{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_smart/columns2

    Admin time setting template.

    Smart 2 column layout template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * navdraweropen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "Mood<PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "navdraweropen":true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false
    }
}}
{{> theme_smart/head }}

<body {{{ bodyattributes }}} data-theme='dark'>
{{> core/local/toast/wrapper}}

<div id="page-wrapper" class="d-print-block">

    {{{ output.standard_top_of_body_html }}}

    {{> theme_smart/navbar }}

    <div id="page" class="container-fluid d-print-block">
        {{{ output.full_header }}}
            {{#secondarymoremenu}}
                <div class="secondary-navigation">
                    {{> core/moremenu}}
                </div>
            {{/secondarymoremenu}}
        <div id="page-content" class="row pb-3 d-print-block">
            <div id="region-main-box" class="col-12">
                {{#hasregionmainsettingsmenu}}
                <div id="region-main-settings-menu" class="d-print-none {{#hasblocks}}has-blocks{{/hasblocks}}">
                    <div> {{{ regionmainsettingsmenu }}} </div>
                </div>
                {{/hasregionmainsettingsmenu}}
                <section id="region-main" {{#hasblocks}}class="has-blocks mb-3"{{/hasblocks}} aria-label="{{#str}}content{{/str}}">

                    {{#hasregionmainsettingsmenu}}
                        <div class="region_main_settings_menu_proxy"></div>
                    {{/hasregionmainsettingsmenu}}
                    {{{ output.course_content_header }}}
                    {{#headercontent}}
                        {{> core/activity_header }}
                    {{/headercontent}}
                    {{#overflow}}
                        {{> core/url_select}}
                    {{/overflow}}
                    {{{ output.main_content }}}
                    {{{ output.activity_navigation }}}
                    {{{ output.course_content_footer }}}

                </section>
                {{#hasblocks}}
                <section data-region="blocks-column" class="d-print-none" aria-label="{{#str}}blocks{{/str}}">
                    {{{ addblockbutton }}}
                    {{{ sidepreblocks }}}
                </section>
                {{/hasblocks}}
            </div>
        </div>
    </div>
    {{{ output.standard_after_main_region_html }}}
    {{> theme_smart/footer }}
</div>

{{{ output.smart_custom_html }}}

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader', 'theme_smart/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_smart/loader');
});
{{/js}}
