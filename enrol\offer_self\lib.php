<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use local_offermanager\trait\offer_enrol_plugin_trait;
use local_offermanager\persistent\offer_class_model;
use enrol_offer_self\self_enrol_form;
use enrol_offer_self\self_message_form;

defined('MOODLE_INTERNAL') || die();
/**
 * Enrolment method Turma de autoinscrição
 *
 * @package    enrol_offer_self
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_offer_self_plugin extends enrol_plugin
{

    use offer_enrol_plugin_trait {
        show_enrolme_link as trait_show_enrolme_link;
        is_self_enrol_available as trait_is_self_enrol_available;
        can_self_enrol as trait_can_self_enrol;
        reenrol_available as trait_reenrol_available;
        enrol_page_hook as trait_enrol_page_hook;
    }

    /**
     * Exibe o link 'Inscreva-me' se a turma estiver disponível e ativa.
     */
    public function show_enrolme_link(stdClass $instance): bool
    {
        return false;
    }

    /**
     * Verifica se a autoinscrição está disponível para a turma.
     *
     * @param stdClass $instance
     * @return true|string Retorna true se disponível, ou HTML com os motivos de indisponibilidade
     */
    public function is_self_enrol_available(stdClass $instance)
    {
        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        $unavailability_reasons = $offerclass->get_unavailability_reasons();

        if (!empty($unavailability_reasons)) {
            return html_writer::alist($unavailability_reasons, ['class' => 'badge-warning rounded py-2 pr-2'], 'ul');
        }

        return true;
    }

    /**
     * Checks if user can self enrol.
     *
     * @param stdClass $instance enrolment instance
     * @param bool $checkuserenrolment if true will check if user enrolment is inactive.
     *             used by navigation to improve performance.
     * @return bool|string true if successful, else error message or false
     */
    public function can_self_enrol(stdClass $instance, $checkuserenrolment = true)
    {
        global $USER;

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if ($checkuserenrolment) {

            if (isguestuser()) {
                return false;
            } else if ($offer_user_enrolment = $offerclass->get_user_offer_user_enrolment($USER->id)) {

                if (!$offer_user_enrolment->has_started()) {

                    $userstimezone = core_date::get_user_timezone();

                    $timestart = $offer_user_enrolment->get_field_from_user_enrolment('timestart');

                    $format = get_string('strftimedatetimeshort', 'langconfig');

                    $timestart = userdate($timestart, $format, $userstimezone);

                    $list = [
                        get_string('user_enrolment_start_on', 'enrol_offer_self', $timestart)
                    ];

                    return html_writer::alist($list, ['class' => 'badge-success rounded py-2 pr-2']);
                }

                if (!$offerclass->get_mapped_field('enablereenrol')) {
                    return html_writer::alist(
                        [
                            get_string('already_enroled', 'enrol_offer_self')
                        ],
                        [
                            'class' => 'badge-warning rounded py-2 pr-2'
                        ]
                    );
                }
            }
        }

        $reasons = $this->is_self_enrol_available($instance);

        if ($reasons !== true) {
            return $reasons;
        }

        return true;
    }

    /**
     * Creates course enrol form, checks if form submitted
     * and enrols user if necessary. It can also redirect.
     *
     * @param stdClass $instance
     * @return string html text, usually a form in a text box
     */
    public function enrol_page_hook(stdClass $instance)
    {
        global $OUTPUT, $USER;

        $context = context_course::instance($instance->courseid);

        if (!has_capability('enrol/offer_self:enrolself', $context)) {
            return false;
        }

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (
            !$offerclass
            || !$offerclass->is_active()
            || $offerclass->is_finished()
        ) {
            return false;
        }

        $offercourse = $offerclass->get_offer_course();

        $offer = $offercourse->get_offer();

        if (!$offer->user_belongs_to_audiences($USER->id)) {
            return false;
        }

        // Verifica se tem matrícula ativa em outra turma
        if ($offercourse->has_active_user_enrolment($USER->id, $instance->id)) {
            return false;
        }

        $can_see_card = true;
        
        $offer_user_enrol = $offerclass->get_user_offer_user_enrolment($USER->id);

        if (
            $can_see_card
            && $offer_user_enrol
            && $offerclass->get_mapped_field('enablereenrol')
            && $offer_user_enrol->is_situation_to_suspend($offer_user_enrol->get('situation'))
        ) {
            $can_see_card = false;
        }

        if ($can_see_card && $offer_user_enrol && $offer_user_enrol->is_canceled()) {
            $can_see_card = false;
        }

        ob_start();
        if ($can_see_card) {
            $enrolstatus = $this->can_self_enrol($instance);

            if ($enrolstatus === true) {

                // This user can self enrol using this instance.
                $form = new self_enrol_form(null, $instance);
                $instanceid = optional_param('instance', 0, PARAM_INT);
                if ($instance->id == $instanceid) {
                    if ($data = $form->get_data()) {
                        $this->enrol_self($instance, $data);
                    }
                }
            } else {
                $data = new stdClass();
                $data->offerclassid = $offerclass->get('id');
                $data->info = $enrolstatus;

                $url = isguestuser() ? get_login_url() : null;
                $form = new self_message_form($url, $data);
            }
            $form->display();
            $output = ob_get_clean();
            return $OUTPUT->box($output);
        }

        // Este aqui renderiza outros formulários que podem ser configurados na trait
        self::trait_enrol_page_hook($instance);
        $output = ob_get_clean();
        return $output;
    }

    /**
     * Self enrol user to course
     *
     * @param stdClass $instance enrolment instance
     * @param stdClass $data data needed for enrolment.
     * @return bool|array true if enroled else eddor code and messege
     */
    public function enrol_self(stdClass $instance)
    {
        global $DB, $USER, $CFG;

        $this->enrol_user($instance, $USER->id, $instance->roleid);

        redirect(
            new moodle_url('/course/view.php', ['id' => $instance->courseid]),
            get_string('youenrolledincourse', 'enrol'),
            2,
            \core\output\notification::NOTIFY_SUCCESS
        );
    }

    /**
     * True or false for allow the enrol plugin to reenrol.
     *
     * @return bool True if reenrolment is available, false otherwise.
     */
    public function reenrol_available()
    {
        return true;
    }
}
