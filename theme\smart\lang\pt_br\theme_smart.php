<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language file.
 *
 * @package   theme_smart
 * @copyright 2023 Revvo
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['advancedsettings'] = 'Configurações avançadas';
$string['backgroundimage'] = 'Imagem de fundo';
$string['backgroundimage_desc'] = 'A imagem a ser exibida como plano de fundo do site. A imagem de fundo que você carregar aqui substituirá a imagem de fundo nos arquivos de tema predefinidos.';
$string['brandcolor'] = 'Cor da marca';
$string['brandcolor_desc'] = 'A cor de destaque.';
$string['bootswatch'] = 'Bootswatch';
$string['bootswatch_desc'] = 'Um bootswatch é um conjunto de variáveis e CSS do Bootstrap para estilizar o Bootstrap.';
$string['choosereadme'] = 'Tema é um tema baseado no boost.';
$string['configtitle'] = 'Tema';
$string['event'] = 'Evento';
$string['generalsettings'] = 'Configurações gerais';
$string['loginbackgroundimage'] = 'Imagem de fundo da página de login';
$string['loginbackgroundimage_desc'] = 'A imagem a ser exibida como plano de fundo da página de login.';
$string['footerlogo'] = "Logo do footer";
$string['footerlogo_desc'] = "Adicione um logo que irá aparecer no footer";
$string['nobootswatch'] = 'Nenhum';
$string['pluginname'] = 'Tema';
$string['presetfiles'] = 'Arquivos de tema predefinidos adicionais';
$string['presetfiles_desc'] = 'Os arquivos predefinidos podem ser usados para alterar drasticamente a aparência do tema. Consulte <a href="https://docs.moodle.org/dev/Tema_Presets">Tema presets</a> para obter informações sobre como criar e compartilhar seus próprios arquivos predefinidos e consulte o <a href="https://archive.moodle.net/tema">Repositório de predefinições</a> para predefinições compartilhadas por outros.';
$string['preset'] = 'Tema predefinido';
$string['preset_desc'] = 'Escolha uma predefinição para alterar amplamente a aparência do tema.';
$string['privacy:metadata'] = 'O tema Tema não armazena nenhum dado pessoal sobre o usuário.';
$string['rawscss'] = 'SCSS bruto';
$string['rawscss_desc'] = 'Use este campo para fornecer código SCSS ou CSS que será injetado no final da folha de estilos.';
$string['rawscsspre'] = 'SCSS inicial bruto';
$string['rawscsspre_desc'] = 'Neste campo, você pode fornecer código SCSS de inicialização, que será injetado antes de tudo. Na maioria das vezes, você usará essa configuração para definir variáveis.';
$string['region-side-pre'] = 'Direita';

$string['coursesettings'] = 'Configurações de curso';
$string['activitynavigationmods'] = 'Habilitar navegação nos módulos';
$string['activitynavigationmods_desc'] = 'Selecione quais modulos terão navegação de atividade habilitada ou deixe sem nenhum selecionado para desabilitar.';

$string['social_title'] = 'Redes Sociais';
$string['socialsettings'] = 'Configurações Sociais';
$string['companyaddress'] = 'Endereço da empresa';
$string['social_facebook'] = 'Facebook';
$string['social_twitter'] = 'Twitter';
$string['social_instagram'] = 'Instagram';
$string['social_linkedin'] = 'Linkedin';
$string['social_youtube'] = 'Youtube';
$string['social_whatsapp'] = 'Whatasapp';

$string['apps_title'] = 'Lojas de aplicativo';
$string['app_applestore'] = 'Apple Store';
$string['app_googleplay'] = 'Google Play';

$string['aisettings'] = 'Configurações de IA';
$string['ai_title'] = 'Configurações de integração Judy';
$string['enable_ai_menu'] = 'Habilitar Judy no menu';
$string['enable_ai_menu_desc'] = 'Habilitar ou desabilitar a integração do Judy no menu.';
$string['ai_assistantid'] = 'ID da assistente';

$string['privacy'] = 'Privacidade';
$string['terms'] = 'Termos';
$string['credits'] = '© ' . date("Y") . " Desenvolvido por ";

$string['ranking'] = 'Ranking';
$string['newpassword'] = "Nova senha";
$string['newpassword_help'] = "A senha deve ter ao menos 8 caracteres, ao menos 1 dígito, ao menos 1 letra minúscula, ao menos 1 letra maiúscula, no mínimo 1 caractere não alfa-numérico, como *, traço, ou hashtag.";

$string['login:access'] = "Acesso";
$string['login:rememberme'] = "Lembrar-me";
$string['login:forgotpassword'] = "Esqueci a senha";
$string['login:loginwith'] = "Fazer login com";
$string['login:backtologin'] = "Voltar ao acesso";
$string['login:lostpassword'] = "Perdeu a senha?";
$string['loginlogo'] = "Logo do login";
$string['loginlogo_desc'] = "Adicione um logo que irá aparecer no login";
$string['login:showpassword'] = "Exibir a senha";
$string['login:hiddenpassword'] = "Esconder a senha";
$string['signup:username_arialabel'] = "Identificação de usuário, Este campo é utilizado para criar um nome de usuário exclusivo na plataforma. Por favor, insira uma combinação de letras, números e caracteres especiais. Evite usar espaços ou caracteres acentuados. Exemplos de nomes válidos: 'usuario123'";
$string['signup:password_arialabel'] = "Senha. ".$string['newpassword_help'];

$string['showfooter'] = 'Mostrar rodapé';
$string['unaddableblocks'] = 'Blocos desnecessários';
$string['unaddableblocks_desc'] = 'Os blocos especificados não são necessários ao usar este tema e não serão listados no menu "Adicionar bloco".';

// MENU BAR //
$string['adminmenu_name'] = 'Administração';
$string['admin_submenu_manageusers'] = 'Gerenciar Usuários';
$string['admin_submenu_managecourses'] = 'Gerenciar Cursos';

// COURSE //
$string['exitcourse'] = 'Sair do curso';
$string['coursemenu'] = 'Menu do curso';

$string['privacy:metadata:preference:draweropenblock'] = 'Preferência do usuário para ocultar ou mostrar o painel com blocos.';
$string['privacy:metadata:preference:draweropenindex'] = 'Preferência do usuário para ocultar ou mostrar o painel com índice de curso.';
$string['privacy:metadata:preference:draweropennav'] = 'Preferência do usuário para ocultar ou mostrar a navegação do menu no painel.';
$string['privacy:drawerindexclosed'] = 'A preferência atual para o painel de índice está fechada.';
$string['privacy:drawerindexopen'] = 'A preferência atual para o painel de índice está aberta.';
$string['privacy:drawerblockclosed'] = 'A preferência atual para o painel de blocos está fechada.';
$string['privacy:drawerblockopen'] = 'A preferência atual para o painel de blocos está aberta.';

$string['footer:sitemap'] = "Mapa do Site";
$string['footer:socialmedia'] = "Redes Sociais";
$string['footer:apps'] = "Nossos Aplicativos";

$string['breadcrumbs:home'] = "Página inicial";
$string['breadcrumbs:catalog'] = "Catálogo";
$string['catalog'] = "Catálogo";

$string['placeholder:search'] = "Buscar...";
$string['message:plural'] = "Mensagens";
$string['message:seeall'] = "Ver todas";
$string['message:noncontacts'] = "Contatos";

$string['forum:format'] = "Formato";
$string['forum:discussion'] = "discussão";
$string['forum:parent'] = "pai";
$string['forum:userfullname'] = "nome completo do usuário";
$string['forum:created'] = "criado";
$string['forum:modified'] = "modificado";
$string['forum:mailed'] = "enviado por email";
$string['forum:subject'] = "assunto";
$string['forum:message'] = "mensagem";
$string['forum:messageformat'] = "formato da mensagem";
$string['forum:messagetrust'] = "confiança da mensagem";
$string['forum:attachment'] = "anexo";
$string['forum:totalscore'] = "pontuação total";
$string['forum:mailnow'] = "enviar agora";
$string['forum:deleted'] = "deletado";
$string['forum:privatereplyto'] = "responder privado";
$string['forum:privatereplytofullname'] = "responder privado para nome completo";
$string['forum:wordcount'] = "contagem de palavras";
$string['forum:charcount'] = "contagem de caracteres";
$string['enable_mustaches_footer'] = 'Habilitar footer para administrador';
$string['enable_mustaches_footer_desc'] = 'Habilitar ou desabilitar o footer para administrador.';
$string['footer_hiden_user'] = 'Habilitar footer para usuário';
$string['footer_hiden_user_desc'] = 'Habilitar ou desabilitar o footer para usuário.';
$string['enable_login_footer'] = 'Habilitar footer para pagina de login';
$string['enable_login_footer_desc'] = 'Habilitar ou desabilitar o footer para pagina de login.';

$string['btn_primary_bg_color'] = "Cor primária";
$string['btn_primary_bg_color_desc'] = "Cor primária dos botões";
$string['btn_primary_text_color'] = "Cor primária do texto";
$string['btn_primary_text_color_desc'] = "Cor primária dos textos dos botões";

$string['required'] = 'Obrigatório';

$string['catalog'] = 'Catálogo';
$string['faleconosco'] = 'Fale Conosco';

// Configurações de HTML personalizado
$string['customhtmlsettings'] = 'Configurações de HTML Personalizado';
$string['custom_html_title'] = 'HTML Personalizado';
$string['custom_html'] = 'Código HTML personalizado';
$string['custom_html_desc'] = 'Insira código HTML personalizado que será adicionado ao final da página. Ideal para scripts de chat, analytics ou outros códigos de terceiros. Use com cuidado e certifique-se de que o código é seguro.';
$string['custom_html_logged_only'] = 'Exibir apenas para usuários logados';
$string['custom_html_logged_only_desc'] = 'Se habilitado, o código HTML personalizado será exibido apenas para usuários autenticados (não para visitantes ou página de login).';